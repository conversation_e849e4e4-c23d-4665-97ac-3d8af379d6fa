<?php
/**
 * Script de diagnóstico para la base de datos
 * Verifica el estado de la conexión y servicios MySQL
 */

// Configuración de la base de datos (misma que DatabaseConnection.php)
$config = [
    'host' => 'localhost',
    'user' => 'ncornejo',
    'password' => 'N1c0l7as17',
    'database' => 'operaciones_tqw',
    'charset' => 'utf8'
];

echo "<h2>🔍 Diagnóstico de Base de Datos - " . date('Y-m-d H:i:s') . "</h2>";

// 1. Verificar si MySQL está ejecutándose
echo "<h3>1. Estado del Servicio MySQL</h3>";
try {
    // Intentar conexión básica sin base de datos específica
    $test_connection = new mysqli($config['host'], $config['user'], $config['password']);
    
    if ($test_connection->connect_error) {
        echo "❌ <strong>Error de conexión:</strong> " . $test_connection->connect_error . "<br>";
        echo "📋 <strong>Código de error:</strong> " . $test_connection->connect_errno . "<br>";
    } else {
        echo "✅ <strong>Conexión al servidor MySQL:</strong> EXITOSA<br>";
        echo "📊 <strong>Versión del servidor:</strong> " . $test_connection->server_info . "<br>";
        echo "🔗 <strong>Protocolo:</strong> " . $test_connection->protocol_version . "<br>";
        
        // Verificar base de datos específica
        echo "<h3>2. Verificación de Base de Datos</h3>";
        $db_check = $test_connection->select_db($config['database']);
        if ($db_check) {
            echo "✅ <strong>Base de datos '{$config['database']}':</strong> ACCESIBLE<br>";
        } else {
            echo "❌ <strong>Base de datos '{$config['database']}':</strong> NO ACCESIBLE<br>";
            echo "📋 <strong>Error:</strong> " . $test_connection->error . "<br>";
        }
        
        $test_connection->close();
    }
} catch (Exception $e) {
    echo "❌ <strong>Excepción capturada:</strong> " . $e->getMessage() . "<br>";
}

// 2. Verificar configuración de red
echo "<h3>3. Configuración de Red</h3>";
echo "🌐 <strong>Host configurado:</strong> " . $config['host'] . "<br>";
echo "👤 <strong>Usuario:</strong> " . $config['user'] . "<br>";
echo "🗄️ <strong>Base de datos:</strong> " . $config['database'] . "<br>";

// 3. Verificar puertos
echo "<h3>4. Verificación de Puertos</h3>";
$mysql_port = 3306;
$connection = @fsockopen($config['host'], $mysql_port, $errno, $errstr, 5);
if ($connection) {
    echo "✅ <strong>Puerto MySQL ($mysql_port):</strong> ABIERTO<br>";
    fclose($connection);
} else {
    echo "❌ <strong>Puerto MySQL ($mysql_port):</strong> CERRADO o INACCESIBLE<br>";
    echo "📋 <strong>Error:</strong> $errstr ($errno)<br>";
}

// 4. Verificar variables del sistema
echo "<h3>5. Variables del Sistema</h3>";
echo "🖥️ <strong>Sistema operativo:</strong> " . PHP_OS . "<br>";
echo "🐘 <strong>Versión PHP:</strong> " . PHP_VERSION . "<br>";
echo "📦 <strong>Extensión MySQLi:</strong> " . (extension_loaded('mysqli') ? '✅ CARGADA' : '❌ NO CARGADA') . "<br>";

// 5. Verificar archivos de configuración
echo "<h3>6. Archivos de Configuración</h3>";
$config_files = [
    'DatabaseConnection.php' => file_exists('DatabaseConnection.php'),
    'login.php' => file_exists('login.php'),
];

foreach ($config_files as $file => $exists) {
    echo "📄 <strong>$file:</strong> " . ($exists ? '✅ EXISTE' : '❌ NO EXISTE') . "<br>";
}

// 6. Verificar procesos MySQL (solo en Windows con WAMP)
echo "<h3>7. Procesos del Sistema</h3>";
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    echo "🪟 <strong>Sistema Windows detectado</strong><br>";
    
    // Verificar si WAMP está ejecutándose
    $wamp_processes = [
        'mysqld.exe',
        'httpd.exe',
        'wampmanager.exe'
    ];
    
    foreach ($wamp_processes as $process) {
        $output = [];
        $return_var = 0;
        exec("tasklist /FI \"IMAGENAME eq $process\" 2>NUL", $output, $return_var);
        
        $running = false;
        foreach ($output as $line) {
            if (stripos($line, $process) !== false) {
                $running = true;
                break;
            }
        }
        
        echo "⚙️ <strong>$process:</strong> " . ($running ? '✅ EJECUTÁNDOSE' : '❌ NO EJECUTÁNDOSE') . "<br>";
    }
} else {
    echo "🐧 <strong>Sistema Unix/Linux detectado</strong><br>";
    echo "ℹ️ Para verificar procesos MySQL, ejecute: <code>ps aux | grep mysql</code><br>";
}

// 7. Recomendaciones
echo "<h3>8. Recomendaciones</h3>";
echo "<div style='background-color: #f0f8ff; padding: 10px; border-left: 4px solid #0066cc;'>";
echo "<strong>Si hay errores de conexión:</strong><br>";
echo "1. ✅ Verificar que WAMP/XAMPP esté ejecutándose<br>";
echo "2. ✅ Reiniciar el servicio MySQL<br>";
echo "3. ✅ Verificar credenciales en DatabaseConnection.php<br>";
echo "4. ✅ Comprobar que el puerto 3306 no esté bloqueado<br>";
echo "5. ✅ Revisar logs de MySQL en: <code>C:\\wamp64\\logs\\mysql.log</code><br>";
echo "</div>";

// 8. Test de conexión completa
echo "<h3>9. Test de Conexión Completa</h3>";
try {
    require_once 'DatabaseConnection.php';
    $db = DatabaseConnection::getInstance();
    $connection = $db->getConnection();
    
    if ($connection) {
        echo "✅ <strong>DatabaseConnection::getInstance():</strong> EXITOSO<br>";
        
        // Test de consulta simple
        $result = $connection->query("SELECT 1 as test");
        if ($result) {
            echo "✅ <strong>Consulta de prueba:</strong> EXITOSA<br>";
            $result->free();
        } else {
            echo "❌ <strong>Consulta de prueba:</strong> FALLIDA<br>";
        }
        
        // Verificar estadísticas de conexión
        $stats = $db->getConnectionStats();
        if ($stats) {
            echo "📊 <strong>Estadísticas de conexión:</strong><br>";
            foreach ($stats as $key => $value) {
                echo "&nbsp;&nbsp;&nbsp;&nbsp;• $key: $value<br>";
            }
        }
        
    } else {
        echo "❌ <strong>DatabaseConnection::getInstance():</strong> FALLIDO<br>";
    }
} catch (Exception $e) {
    echo "❌ <strong>Error en test completo:</strong> " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<p><em>Diagnóstico completado a las " . date('Y-m-d H:i:s') . "</em></p>";
?>
