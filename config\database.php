<?php
/**
 * Secure Database Configuration
 * Environment-based configuration management
 * 
 * SECURITY NOTE: This file should be placed outside the web root
 * or protected by .htaccess to prevent direct access
 */

// Prevent direct access
if (!defined('APP_ACCESS')) {
    http_response_code(403);
    exit('Access Denied');
}

/**
 * Environment Detection
 * Determines if we're in development or production
 */
function getEnvironment() {
    // Check for environment variable first
    $env = getenv('APP_ENV');
    if ($env) {
        return $env;
    }
    
    // Fallback: detect by server characteristics
    $serverName = $_SERVER['SERVER_NAME'] ?? '';
    $serverAddr = $_SERVER['SERVER_ADDR'] ?? '';
    
    // Development indicators
    $devIndicators = [
        'localhost',
        '127.0.0.1',
        '::1',
        '.local',
        '.dev',
        '.test'
    ];
    
    foreach ($devIndicators as $indicator) {
        if (strpos($serverName, $indicator) !== false || 
            strpos($serverAddr, $indicator) !== false) {
            return 'development';
        }
    }
    
    return 'production';
}

/**
 * Get Database Configuration
 * Returns configuration based on environment
 */
function getDatabaseConfig() {
    $environment = getEnvironment();
    
    switch ($environment) {
        case 'development':
            return [
                'host' => getenv('DB_HOST') ?: 'localhost',
                'user' => getenv('DB_USER') ?: 'ncornejo',
                'password' => getenv('DB_PASS') ?: 'N1c0l7as17',
                'database' => getenv('DB_NAME') ?: 'operaciones_test',
                'charset' => 'utf8mb4',
                'port' => getenv('DB_PORT') ?: 3306,
                'ssl' => false,
                'debug' => true,
                'log_errors' => true,
                'show_errors' => false // Never show errors to users, even in dev
            ];
            
        case 'production':
        default:
            return [
                'host' => getenv('DB_HOST') ?: 'localhost',
                'user' => getenv('DB_USER') ?: 'ncornejo',
                'password' => getenv('DB_PASS') ?: 'N1c0l7as17',
                'database' => getenv('DB_NAME') ?: 'operaciones_tqw',
                'charset' => 'utf8mb4',
                'port' => getenv('DB_PORT') ?: 3306,
                'ssl' => true,
                'debug' => false,
                'log_errors' => true,
                'show_errors' => false
            ];
    }
}

/**
 * Get Security Configuration
 */
function getSecurityConfig() {
    $environment = getEnvironment();
    
    return [
        'environment' => $environment,
        'debug_mode' => $environment === 'development',
        'log_level' => $environment === 'development' ? 'DEBUG' : 'ERROR',
        'session_timeout' => 21600, // 6 hours
        'max_login_attempts' => 5,
        'lockout_duration' => 900, // 15 minutes
        'secure_cookies' => $environment === 'production',
        'csrf_protection' => true,
        'rate_limiting' => true
    ];
}

/**
 * Secure Error Logging
 */
function logSecureError($message, $context = [], $level = 'ERROR') {
    $config = getSecurityConfig();
    
    // Only log if logging is enabled
    if (!$config['log_errors']) {
        return;
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $sessionId = session_id() ?: 'NO_SESSION';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN';
    $remoteAddr = $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN';
    $requestUri = $_SERVER['REQUEST_URI'] ?? 'UNKNOWN';
    
    // Sanitize sensitive information from context
    $sanitizedContext = sanitizeLogContext($context);
    
    $logEntry = [
        'timestamp' => $timestamp,
        'level' => $level,
        'message' => $message,
        'context' => $sanitizedContext,
        'session_id' => $sessionId,
        'user_agent' => $userAgent,
        'remote_addr' => $remoteAddr,
        'request_uri' => $requestUri,
        'environment' => $config['environment']
    ];
    
    $logLine = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . PHP_EOL;
    
    // Write to secure log file
    $logFile = __DIR__ . '/../logs/security_' . date('Y-m-d') . '.log';
    
    // Ensure log directory exists and is secure
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0750, true);
        // Create .htaccess to protect log directory
        file_put_contents($logDir . '/.htaccess', "Deny from all\n");
    }
    
    error_log($logLine, 3, $logFile);
}

/**
 * Sanitize log context to remove sensitive information
 */
function sanitizeLogContext($context) {
    $sensitiveKeys = [
        'password', 'passwd', 'pwd', 'secret', 'token', 'key', 'auth',
        'credential', 'pass', 'api_key', 'private_key', 'session'
    ];
    
    $sanitized = [];
    
    foreach ($context as $key => $value) {
        $lowerKey = strtolower($key);
        $isSensitive = false;
        
        foreach ($sensitiveKeys as $sensitiveKey) {
            if (strpos($lowerKey, $sensitiveKey) !== false) {
                $isSensitive = true;
                break;
            }
        }
        
        if ($isSensitive) {
            $sanitized[$key] = '[REDACTED]';
        } else {
            $sanitized[$key] = is_string($value) ? substr($value, 0, 1000) : $value;
        }
    }
    
    return $sanitized;
}

/**
 * Get user-safe error message
 */
function getUserSafeErrorMessage($errorType = 'general') {
    $messages = [
        'database' => 'Servicio temporalmente no disponible. Por favor, intente más tarde.',
        'connection' => 'Error de conexión. Verifique su conexión a internet.',
        'authentication' => 'Credenciales incorrectas. Verifique su usuario y contraseña.',
        'authorization' => 'No tiene permisos para acceder a este recurso.',
        'validation' => 'Los datos ingresados no son válidos.',
        'session' => 'Su sesión ha expirado. Por favor, inicie sesión nuevamente.',
        'rate_limit' => 'Demasiados intentos. Por favor, espere antes de intentar nuevamente.',
        'general' => 'Ha ocurrido un error inesperado. Por favor, contacte al administrador.'
    ];
    
    return $messages[$errorType] ?? $messages['general'];
}
?>
