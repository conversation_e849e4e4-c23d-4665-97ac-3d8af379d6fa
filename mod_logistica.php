<?php
/**
 * mod_logistica.php
 * Versión PHP del módulo de logística con integración a base de datos
 */

// Configuración de errores (para desarrollo)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir el gestor de sesión centralizado (igual que activity_dashboard.php)
require_once 'includes/session_manager.php';

// Incluir conexión a la base de datos
$inc = include("con_db.php");

// La sesión ya está iniciada por session_manager.php
// Verificar si el usuario ha iniciado sesión usando las mismas variables que activity_dashboard.php
if (!isset($_SESSION['rut']) || empty($_SESSION['rut']) || !isset($_SESSION['id_sesion']) || empty($_SESSION['id_sesion'])) {
    // Función para mostrar mensaje de sesión no iniciada
    function mostrarMensajeSesion() {
        // Almacenar la URL actual en localStorage para referencia
        echo '<!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Sesión Requerida</title>
            <link rel="stylesheet" href="css/bootstrap.min.css">
            <!-- Estilos movidos a tecnico_home_logis.css -->
        </head>
        <body>
            <div class="session-card">
                <h3>Sesión no encontrada</h3>
                <p class="message">La sesión no se encuentra activa o ha expirado.</p>
                <div class="btn-group">
                    <button id="btnVolver" class="btn btn-secondary">Volver Atrás</button>
                    <a href="login.php" class="btn btn-primary">Iniciar Sesión</a>
                </div>

                <div class="debug-info">
                    <p>Si estás viendo esta pantalla frecuentemente, es posible que tu sesión no se esté guardando correctamente.</p>
                    <p>Intenta las siguientes acciones:</p>
                    <ul>
                        <li>Eliminar cookies y caché del navegador</li>
                        <li>Abrir el enlace en una nueva pestaña</li>
                        <li>Usar el enlace "Iniciar Sesión" para iniciar una nueva sesión</li>
                    </ul>
                </div>
            </div>

            <script>
            // Registrar la página actual y anterior en localStorage
            if (typeof localStorage !== "undefined") {
                var currentPage = window.location.href;
                var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");

                // Añadir la página actual al historial si no está ya
                if (previousPages.indexOf(currentPage) === -1) {
                    previousPages.push(currentPage);
                    // Mantener solo las últimas 5 páginas
                    if (previousPages.length > 5) {
                        previousPages = previousPages.slice(-5);
                    }
                    localStorage.setItem("navigationHistory", JSON.stringify(previousPages));
                }
            }

            // Mejorar el funcionamiento del botón volver atrás
            document.getElementById("btnVolver").addEventListener("click", function(e) {
                e.preventDefault();

                // Primero intentar con el historial del navegador
                if (window.history.length > 1) {
                    window.history.go(-1);
                    return;
                }

                // Si no hay historial de navegador, intentar con localStorage
                if (typeof localStorage !== "undefined") {
                    var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");

                    if (previousPages.length > 1) {
                        // Ir a la página anterior en el historial
                        var previousPage = previousPages[previousPages.length - 2];
                        window.location.href = previousPage;
                    } else {
                        // Si no hay historial, ir a la página de inicio
                        window.location.href = "index.php";
                    }
                } else {
                    // Fallback si localStorage no está disponible
                    window.location.href = "index.php";
                }
            });
            </script>
        </body>
        </html>';
        exit;
    }

    mostrarMensajeSesion();
}

// Recuperar variables de sesión del usuario (igual que activity_dashboard.php)
$rutSesion = isset($_SESSION['rut']) ? $_SESSION['rut'] : '';
$idSesion = isset($_SESSION['id_sesion']) ? $_SESSION['id_sesion'] : '';

// Obtener información del usuario desde la base de datos usando el token de sesión
// (similar al patrón usado en otros archivos como bodega_directa.php, etc.)
$sql3 = "SELECT tut.nombre, tut.email, tla.RUT, tut.id, tut.PERFIL, tut.nombre_short, tut.area
         FROM TB_LOG_APP tla
         LEFT JOIN tb_user_tqw tut
         ON tla.RUT = tut.rut
         WHERE TOKEN = '$idSesion'";

$result = mysqli_query($conex, $sql3);
if ($result && mysqli_num_rows($result) > 0) {
    $row = mysqli_fetch_assoc($result);
    $id_usuario = $row['id'];
    $nombre_usuario = $row['nombre'];
    $nombre_short = $row['nombre_short'] ?? "";
    $rut_usuario = $row['RUT'];
    $area_usuario = $row['area'] ?? "";
    $perfil_usuario = $row['PERFIL'] ?? "";
} else {
    // Si no se encuentra el usuario en la base de datos, mostrar error
    mostrarMensajeSesion();
}

// ============================================================================
// LAZY LOADING IMPLEMENTATION - Optimización de rendimiento
// ============================================================================
// Las consultas SQL se han movido a api_mod_logis_load_table para carga bajo demanda
// Esto mejora significativamente el tiempo de carga inicial de la página

// Variables para mantener compatibilidad con el código existente
$resultado_directa = null;
$resultado_faltante = null;
$resultado_recepcion = null;
$resultado_reversa = null;

// Flag para indicar que el lazy loading está habilitado
$lazy_loading_enabled = true;

// Las consultas originales están ahora en api_mod_logis_load_table y se ejecutan
// solo cuando el usuario hace clic en las pestañas correspondientes

// Las consultas SQL originales han sido movidas a api_mod_logis_load_table
// para implementar lazy loading y mejorar el rendimiento

// Todas las consultas SQL han sido movidas a api_mod_logis_load_table para lazy loading

?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Módulo Logística</title><!-- Updated -->

  <!-- Favicon - Comentado temporalmente para evitar errores de carga -->
  <!-- <link rel="icon" type="image/x-icon" href="img/core-img/favicon.ico"> -->

  <!-- Preload de recursos - Comentado temporalmente para evitar errores de carga -->
  <!--
  <link rel="preload" href="css/footer_modular.css" as="style">
  <link rel="preload" href="js/core/footer_modular.js" as="script">
  <link rel="preload" href="components/footer_modular.php" as="fetch" crossorigin>

  <link rel="preload" href="js/lazy-loading-enhanced.js" as="script">
  <link rel="preload" href="js/export-tables.js" as="script">
  <link rel="preload" href="js/table-config.js" as="script">
  -->
  
  <!-- Estilos base -->
  <link rel="stylesheet" href="css/activity_dashboard.css">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <link rel="stylesheet" href="css/floating-menu.css">
  <link rel="stylesheet" href="css/metric-cards.css">
  <link rel="stylesheet" href="css/collapsible-sections.css">
  <link rel="stylesheet" href="css/form_panels.css">
  <link rel="stylesheet" href="css/logout-modal.css">
  <link rel="stylesheet" href="css/mod_logistica.css">
  <link rel="stylesheet" href="css/footer_modular.css"><!-- CSS del footer modular -->
  <link rel="stylesheet" href="css/floating_menu.css"><!-- CSS del menú flotante -->
  <link rel="stylesheet" href="css/footer-fix-corrected.css"><!-- Corrección para el footer (versión corregida) -->
  <link rel="stylesheet" href="css/button-fix.css"><!-- Estandarización del botón + -->
  <link rel="stylesheet" href="css/mod_logistica_responsive.css">
  
  <!-- Todos los estilos han sido movidos a css/mod_logistica.css -->
</head>
<body class="mod-logistica dark-theme">
  <!-- Overlay para cerrar el menú flotante -->
  <div class="menu-overlay" id="menuOverlay"></div>
  <!-- Overlay para cerrar los formularios -->
  <div class="form-overlay" id="formOverlay"></div>

  <div class="app-container">
    <!-- Header Section -->
    <header class="app-header">
      <h1>Módulo Logística</h1><!-- Updated -->
    </header>

    <!-- Filtros de búsqueda -->
    <div class="search-filter-container">
      <!-- Tabs de navegación -->
      <div class="tabs-container">
        <button class="tab-button" data-tab="faltante">Faltante</button>
        <button class="tab-button active" data-tab="recepcion">Recepción</button>
        <button class="tab-button" data-tab="directa">Directa</button>
        <button class="tab-button" data-tab="reversa">Reversa</button>
      </div>
      
      <div class="d-flex justify-content-between align-items-center w-100 mt-1">
        <div class="search-box w-100 position-relative">
          <label for="searchInput" class="sr-only">Buscar en todas las tablas</label>
          <input type="text" id="searchInput" placeholder="Buscar en todas las tablas..." class="search-input" style="padding-right: 40px;">
          <button type="button" id="clearSearch" class="btn btn-clear-search position-absolute" aria-label="Limpiar búsqueda" style="right: 8px; top: 50%; transform: translateY(-50%); border: none; background: transparent; color: #6c757d; padding: 4px 8px; z-index: 10;">
            <i class="bi bi-trash"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="activity-section">
      <div class="activity-metrics">
        <!-- Contenedor para todas las tablas -->
        <div class="tables-container">
          <!-- Tabla para Directa -->
          <div class="table-section" data-table="directa">
            <h3 class="table-title">Directa</h3>
            <div class="table-container">
              <table class="data-table" id="directaTable">
                <thead>
                  <tr>
                    <th class="sortable" data-sort="text">Serie <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th class="sortable" data-sort="text">Estado <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody id="directaTableBody">
                  <!-- Lazy Loading: Los datos se cargarán dinámicamente -->
                  <tr class="loading-row">
                    <td colspan="3" class="text-center">
                      <div class="loading-container">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                          <span class="visually-hidden">Cargando...</span>
                        </div>
                        Haga clic en la pestaña "Directa" para cargar los datos
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Tabla para Faltante -->
          <div class="table-section" data-table="faltante">
            <h3 class="table-title">Faltante</h3>
            <div class="table-container">
              <table class="data-table" id="faltanteTable">
                <thead>
                  <tr>
                    <th class="sortable" data-sort="text">Serie <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th class="sortable" data-sort="text">Estado <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody id="faltanteTableBody">
                  <!-- Lazy Loading: Los datos se cargarán dinámicamente -->
                  <tr class="loading-row">
                    <td colspan="3" class="text-center">
                      <div class="loading-container">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                          <span class="visually-hidden">Cargando...</span>
                        </div>
                        Haga clic en la pestaña "Faltante" para cargar los datos
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Tabla para Recepción -->
          <div class="table-section" data-table="recepcion">
            <h3 class="table-title">Recepción</h3>
            <div class="table-container">
              <table class="data-table" id="recepcionTable">
                <thead>
                  <tr>
                    <th class="sortable" data-sort="text">Serie <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th class="sortable" data-sort="text">Estado <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody id="recepcionTableBody">
                  <!-- Lazy Loading: Los datos se cargarán dinámicamente -->
                  <tr class="loading-row">
                    <td colspan="3" class="text-center">
                      <div class="loading-container">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                          <span class="visually-hidden">Cargando...</span>
                        </div>
                        Haga clic en la pestaña "Recepción" para cargar los datos
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Tabla para Reversa -->
          <div class="table-section" data-table="reversa">
            <h3 class="table-title">Reversa</h3>
            <div class="table-container">
              <table class="data-table" id="reversaTable">
                <thead>
                  <tr>
                    <th class="sortable" data-sort="text">Serie <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th class="sortable" data-sort="text">Estado <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody id="reversaTableBody">
                  <!-- Lazy Loading: Los datos se cargarán dinámicamente -->
                  <tr class="loading-row">
                    <td colspan="3" class="text-center">
                      <div class="loading-container">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                          <span class="visually-hidden">Cargando...</span>
                        </div>
                        Haga clic en la pestaña "Reversa" para cargar los datos
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

  <!-- Offcanvas para historial -->
  <div class="offcanvas offcanvas-end" id="offcanvasHistorial" data-bs-scroll="true" tabindex="-1" aria-labelledby="offcanvasHistorialLabel">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasHistorialLabel">Historial de movimiento</h5>
      <button class="btn-close text-reset" type="button" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body p-4">
      <div class="mb-3">
        <label for="serieHistorial" class="form-label">SERIE</label>
        <input type="text" class="form-control" id="serieHistorial" name="serieHistorial" readonly>
      </div>

      <div class="mb-3">
        <label for="precioHistorial" class="form-label">PRECIO</label>
        <input type="text" class="form-control" id="precioHistorial" name="precioHistorial" readonly>
      </div>

      <div class="timeline-container" id="webHistorial">
        <!-- El historial se cargará dinámicamente aquí -->
      </div>
    </div>
  </div>

  <!-- End of metrics sections -->
  </div> <!-- Cierre del div.app-container -->

  <!-- Formulario de Solicitud -->
  <div class="form-panel" id="solicitudFormPanel">
    <div class="form-container">
      <div class="form-header">
        <h2 class="form-title"><i class="bi bi-clipboard-check"></i> Formulario de Solicitud</h2>
        <button type="button" class="close-form-btn" title="Cerrar formulario" aria-label="Cerrar formulario"><i class="bi bi-x-lg"></i></button>
      </div>
      <form id="solicitudForm">
        <div class="form-group">
          <label for="tipo_solicitud" class="form-label">Tipo de Solicitud</label>
          <select id="tipo_solicitud" class="form-select" required>
            <option value="" selected disabled>Seleccione el tipo de solicitud</option>
            <option value="material">Material</option>
            <option value="herramienta">Herramienta</option>
            <option value="transporte">Transporte</option>
          </select>
        </div>

        <div class="form-group">
          <label for="fecha_solicitud" class="form-label">Fecha</label>
          <input type="date" id="fecha_solicitud" class="date-input" required>
        </div>

        <div class="form-group">
          <label for="solicitante" class="form-label">RUT del Solicitante</label>
          <input type="text" id="solicitante" class="form-control" placeholder="Ingrese el RUT del solicitante" required>
        </div>

        <div class="form-group">
          <label for="descripcion_solicitud" class="form-label">Descripción</label>
          <textarea id="descripcion_solicitud" class="form-textarea" placeholder="Ingrese los detalles de la solicitud" required></textarea>
        </div>

        <div class="form-buttons">
          <button type="button" class="btn-cancel">Cancelar</button>
          <button type="submit" class="btn-submit">Enviar</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Navegación inferior -->
  <nav class="bottom-nav" id="bottom-nav">
    <!-- Iconos a la izquierda -->
    <a href="activity_dashboard.php" class="nav-item">
      <i class="bi bi-grid"></i>
    </a>
    <a href="charts_dashboard.php" class="nav-item">
      <i class="bi bi-activity"></i>
    </a>
    <a href="calidad_reactiva.php" class="nav-item">
      <i class="bi bi-file-text"></i>
    </a>

    <!-- Botón + central -->
    <div class="add-button-container">
      <div class="floating-menu" id="floatingMenu">
        <a href="#" class="floating-menu-item" data-form-id="materiales">
          <i class="bi bi-box-seam"></i> Solicitud de Materiales
        </a>
      </div>
      <div class="nav-item add-button" id="addButton">
        <i class="bi bi-plus"></i>
      </div>
    </div>

    <!-- Iconos a la derecha -->
    <a href="javascript:void(0)" class="nav-item" id="ticketButton">
      <i class="bi bi-ticket"></i>
    </a>
    <a href="Tecnico_Home_LOGIS_TEST_V2.php" class="nav-item">
      <i class="bi bi-house"></i>
    </a>
    <a href="logout.php" class="nav-item" id="logoutButton">
      <i class="bi bi-box-arrow-right"></i>
    </a>
  </nav>

  <!-- Overlays necesarios para el footer modular -->
  <div class="menu-overlay" id="menuOverlay"></div>
  <div class="form-overlay" id="formOverlay"></div>

  <!-- Contenedor de formularios dinámicos -->
  <div id="formsContainer">
    <div class="form-panel" id="form-materiales" data-form-id="materiales">
      <div class="form-placeholder">Formulario de materiales no encontrado</div>
    </div>
  </div>

  <!-- Offcanvas TRANSFERENCIA (Canvas 7) -->
  <div class="offcanvas offcanvas-end" id="offcanvasrigh" data-bs-scroll="true" tabindex="-1"
      aria-labelledby="affanOffcanvsLabel" style="z-index: 1045;" data-bs-backdrop="true">
      <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
          aria-label="Close"></button>

      <div class="offcanvas-body p-4">
          <h5>Canal de requerimientos</h5>

          <div class="mb-3">
              <label for="serie_tran" class="form-label">SERIE SELECCIONADA</label>
              <input type="text" class="form-control" id="serie_tran" name="serie_tran" value="" readonly required>
          </div>

          <h5>ESCALAMIENTO A SUPERVISOR</h5>

          <a class="btn m-1 btn-info" href="#" id="justificarLink">
              <i class="bi bi-cursor"></i> Validación supervisor
          </a>

          <a class="btn m-1 btn-info" href="#" id="bodegaSistemico">
              <i class="bi bi-cursor"></i> PROBLEMA SISTEMICO
          </a>

          <a class="btn m-1 btn-info" href="#" id="bodegaSeriIncorrecta">
              <i class="bi bi-cursor"></i> Equipo serie incorrecta
          </a>

          <h5 style="MARGIN-TOP: 15px;"> ESCALAMIENTO A BODEGA </h5>

          <div class="d-flex flex-column gap-2">

              <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="bodegaLink">
                  <i class="bi bi-cursor"></i>
                  <span>Equipo con desperfecto</span>
              </a>

              <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="bodegaLinkTOA">
                  <i class="bi bi-cursor"></i>
                  <span>Serie no aparece en TOA</span>
              </a>

              <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="fotoCierreInv">
                  <i class="bi bi-cursor"></i>
                  <span>Serie a regularizar por cierre de inventario</span>
              </a>

              <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="DevueltoBodega">
                  <i class="bi bi-cursor"></i>
                  <span>Devuelto a bodega</span>
              </a>

          </div>

          <h5 style="MARGIN-TOP: 15px;"> TRANSFERENCIA ENTRE TECNICOS </h5>

          <a class="btn m-1 btn-warning" style="margin-bottom:10px;margin-top:10px;" href="#" id="tecnicoLink">
              <i class="bi bi-cursor"></i> Transferencia a otro tecnico
          </a>

          <!-- Etiqueta hr como separador -->
          <hr class="separador">

          <div class="mb-3" id="tecnicoTransf" style="display: none;">
              <label for="tecnico_select" class="form-label">Técnico a quien transfiere <span class="text-danger">*</span></label>
              <select class="form-select form-select-sm form-control-clicked" id="usuario_destino" name="usuario_destino" required>
                  <option value="">Seleccione el técnico a transferir</option>
                  <!-- Opciones de técnicos se cargarán dinámicamente -->
              </select>
              <div class="invalid-feedback" id="usuario_destino_error">
                  Por favor seleccione un técnico de destino.
              </div>
          </div>

          <input type="hidden" id="usuario_destino_hidden" name="usuario_destino_hidden">

          <div class="mb-3" id="serie_tran_contain" style="display: none;">
              <label for="serie_wrong_directa" class="form-label">INGRESE LA SERIE <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="serie_tran_new" name="serie_tran_new" value="" required
                     placeholder="Ingrese la serie del equipo">
              <div class="invalid-feedback" id="serie_tran_new_error">
                  Por favor ingrese una serie válida.
              </div>
          </div>

          <div id="divArchivo" class="form-group" style="display: none;">
              <label class="form-label" for="customFile3">Cargar Foto <span class="text-muted">(Opcional)</span></label>
              <input class="form-control" name="fileInventario" id="fileInventario" type="file"
                     accept="image/*,.pdf,.doc,.docx">
              <div class="form-text">Formatos permitidos: JPG, PNG, PDF, DOC, DOCX. Tamaño máximo: 5MB</div>
              <div class="invalid-feedback" id="fileInventario_error">
                  El archivo seleccionado no es válido.
              </div>
          </div>

          <div class="mb-3" id="motivo_tran_contain" style="display: none;">
              <label id="serie_incorrecta_directa" for="motivo_tran" class="form-label">MOTIVO <span class="text-danger">*</span></label>
              <textarea class="form-control" id="motivo_tran" name="motivo_tran" rows="4" required
                        placeholder="Describa el motivo de la transferencia"></textarea>
              <div class="invalid-feedback" id="motivo_tran_error">
                  Por favor ingrese un motivo válido (mínimo 10 caracteres).
              </div>
          </div>

          <!-- Agregar el combo box -->
          <div class="mb-3" id="motivoASuper" style="display: none;">
              <label class="form-label" for="defaultSelectSm">Seleccionar motivo <span class="text-danger">*</span></label>
              <select class="form-select form-select-sm form-control-clicked" id="defaultSelectSm"
                  name="defaultSelectSm" aria-label="Default select example" required>
                  <option value="" selected>SELECCIONAR</option>
                  <option value="perdidaTecnico">PERDIDA MATERIAL POR TECNICO</option>
                  <option value="robo">ROBO</option>
                  <option value="dano">DAÑO EN EQUIPO</option>
                  <option value="falla_tecnica">FALLA TÉCNICA</option>
                  <option value="otro">OTRO MOTIVO</option>
              </select>
              <div class="invalid-feedback" id="defaultSelectSm_error">
                  Por favor seleccione un motivo.
              </div>
          </div>
          
          <div class="mb-3">
              <input type="hidden" class="form-control" id="val_tecnico_destino" name="val_tecnico_destino">
          </div>

          <button type="button" class="btn btn-success" id="transferButton">Enviar requerimiento</button>
      </div>
  </div>

  <!-- Offcanvas INSTALACION de serie (Canvas 8) -->
  <div class="offcanvas offcanvas-end" id="offcanvasInstala" data-bs-scroll="true" tabindex="-1"
      aria-labelledby="affanOffcanvsLabel">

      <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
          aria-label="Close"></button>

      <div class="offcanvas-body p-4">
          <h5>declara la serie instalada</h5>

          <form method="POST">
              <div class="mb-3">
                  <label for="serie_insta" class="form-label">SERIE SELECCIONADA</label>
                  <input type="text" class="form-control" placeholder="Ingrese la serie" id="serie_insta"
                      name="serie_insta" value="" required readonly>
              </div>

              <div class="mb-3">
                  <label for="formOTinsta" class="form-label">Orden de trabajo <span class="text-danger">*</span></label>
                  <input type="text" class="form-control" placeholder="Ingrese la orden de trabajo" id="formOT_insta"
                      name="formOT_insta" value="" required pattern="[0-9A-Za-z\-]+"
                      title="Solo se permiten números, letras y guiones">
                  <div class="invalid-feedback" id="formOT_insta_error">
                      Por favor ingrese una orden de trabajo válida.
                  </div>
              </div>

              <div class="mb-3">
                  <label for="rut_insta" class="form-label">RUT cliente <span class="text-muted">(Opcional)</span></label>
                  <input type="text" class="form-control" placeholder="Ej: 12345678-9" id="rut_insta"
                      name="rut_insta" value="" pattern="[0-9]{7,8}-[0-9kK]{1}"
                      title="Formato: 12345678-9">
                  <div class="form-text">Formato: 12345678-9 (opcional)</div>
                  <div class="invalid-feedback" id="rut_insta_error">
                      Por favor ingrese un RUT válido (formato: 12345678-9).
                  </div>
              </div>

              <div class="form-group">
                  <label class="form-label" for="obs_insta">Observaciones <span class="text-danger">*</span></label>
                  <textarea class="form-control" id="obs_insta"
                      placeholder="Describa los detalles de la instalación" name="obs_insta" cols="3" rows="5"
                      required minlength="10" maxlength="500"></textarea>
                  <div class="form-text">Mínimo 10 caracteres, máximo 500 caracteres</div>
                  <div class="invalid-feedback" id="obs_insta_error">
                      Por favor ingrese observaciones válidas (mínimo 10 caracteres).
                  </div>
              </div>

              <div class="form-group">
                  <label class="form-label" for="fileInsta">Cargar Archivo <span class="text-muted">(Opcional)</span></label>
                  <input class="form-control" name="fileInsta" id="fileInsta" type="file"
                         accept="image/*,.pdf,.doc,.docx">
                  <div class="form-text">Formatos permitidos: JPG, PNG, PDF, DOC, DOCX. Tamaño máximo: 5MB</div>
                  <div class="invalid-feedback" id="fileInsta_error">
                      El archivo seleccionado no es válido.
                  </div>
              </div>

              <button type="button" class="btn btn-success" id="instalButton">Declarar instalada</button>
          </form>
      </div>
  </div>

  <!-- Offcanvas TRANSFERENCIA REVERSA (Canvas 9) -->
  <div class="offcanvas offcanvas-end" id="offcanvasrevSuper" data-bs-scroll="true" tabindex="-1"
      aria-labelledby="affanOffcanvsLabel">

      <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
          aria-label="Close"></button>

      <div class="offcanvas-body p-4">
          <h5>Validación supervisor en reversa</h5>

          <div class="mb-3">
              <label for="serie_tran" class="form-label">SERIE SELECCIONADA</label>
              <input type="text" class="form-control" id="serie_trans_rever" name="serie_trans_rever" value=""
                  readonly required>
          </div>

          <div class="mb-3">
              <label for="rutReversa" class="form-label">RUT <span class="text-muted">(Opcional)</span></label>
              <input type="text" class="form-control" id="rutReversa" name="rutReversa"
                     placeholder="Ej: 12345678-9" pattern="[0-9]{7,8}-[0-9kK]{1}"
                     title="Formato: 12345678-9">
              <div class="form-text">Formato: 12345678-9 (opcional)</div>
              <div class="invalid-feedback" id="rutReversa_error">
                  Por favor ingrese un RUT válido (formato: 12345678-9).
              </div>
          </div>

          <div class="mb-3">
              <label for="ordenReversa" class="form-label">ORDEN DE TRABAJO <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="ordenReversa" name="ordenReversa" required
                     placeholder="Ingrese la orden de trabajo" pattern="[0-9A-Za-z\-]+"
                     title="Solo se permiten números, letras y guiones">
              <div class="invalid-feedback" id="ordenReversa_error">
                  Por favor ingrese una orden de trabajo válida.
              </div>
          </div>

          <div class="mb-3">
              <label for="super_destino" class="form-label">Transferencia supervisor <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="super_destino" name="super_destino"
                  value="" list="list_tecnico" readonly required placeholder="Se asignará automáticamente">
              <div class="invalid-feedback" id="super_destino_error">
                  Por favor asigne un supervisor de destino.
              </div>
          </div>

          <div class="form-group mb-3">
              <label class="form-label" id="label_motivo" for="listReversa">Seleccionar motivo <span class="text-danger">*</span></label>
              <select class="form-select form-select-sm form-control-clicked" id="listReversa" name="listReversa"
                  aria-label="Seleccionar motivo" required>
                  <option value="" selected>SELECCIONAR</option>
                  <option value="perdidaTecnico">PERDIDA DE CLIENTE</option>
                  <option value="robo">ROBO</option>
                  <option value="Serie Incorrecta">SERIE EQUIPO INCORRECTA</option>
                  <option value="serieMalDesprovisionada">SERIE MAL DESPROVISIONADA</option>
                  <option value="serieInstalada">Equipo instalado en reversa</option>
                  <option value="dano_equipo">DAÑO EN EQUIPO</option>
                  <option value="falla_tecnica">FALLA TÉCNICA</option>
              </select>
              <div class="invalid-feedback" id="listReversa_error">
                  Por favor seleccione un motivo.
              </div>
          </div>

          <div class="mb-3">
              <label for="serieNewReversa" class="form-label">SERIE FISICA RETIRADA <span class="text-danger">*</span></label>
              <input type="text" class="form-control" placeholder="Ingrese la serie física retirada"
                     id="serieNewReversa" name="serieNewReversa" required
                     pattern="[A-Za-z0-9\-]+" title="Solo se permiten letras, números y guiones">
              <div class="invalid-feedback" id="serieNewReversa_error">
                  Por favor ingrese una serie válida.
              </div>
          </div>

          <div class="form-group">
              <label class="form-label" for="obs_rev_tra">Observaciones <span class="text-danger">*</span></label>
              <textarea class="form-control" id="obs_rev_tra" name="obs_rev_tra" cols="3" rows="5"
                  required minlength="10" maxlength="500"
                  placeholder="Describa los detalles de la transferencia reversa"></textarea>
              <div class="form-text">Mínimo 10 caracteres, máximo 500 caracteres</div>
              <div class="invalid-feedback" id="obs_rev_tra_error">
                  Por favor ingrese observaciones válidas (mínimo 10 caracteres).
              </div>
          </div>

          <div class="form-group">
              <label class="form-label" for="userfile">Cargar Archivo <span class="text-muted">(Opcional)</span></label>
              <input class="form-control" name="userfile" id="userfile" type="file"
                     accept="image/*,.pdf,.doc,.docx">
              <div class="form-text">Formatos permitidos: JPG, PNG, PDF, DOC, DOCX. Tamaño máximo: 5MB</div>
              <div class="invalid-feedback" id="userfile_error">
                  El archivo seleccionado no es válido.
              </div>
          </div>

          <button type="button" class="btn btn-warning" id="transferReversaButton">Solicitar requerimiento</button>
      </div>
  </div>

  <!-- Offcanvas ENTREGA DE REVERSA (Canvas 10) -->
  <div class="offcanvas offcanvas-end" id="offcanvasReversaDeclara" data-bs-scroll="true" tabindex="-1"
      aria-labelledby="affanOffcanvsLabel">

      <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
          aria-label="Close"></button>

      <div class="offcanvas-body p-4">
          <h5>Declaración de entrega para reversa</h5>

          <div class="mb-3">
              <label for="serie_trans_rever" class="form-label">SERIE SELECCIONADA</label>
              <input type="text" class="form-control" id="serieReversaDeclara" name="serie_trans_rever" value=""
                  readonly required>
          </div>

          <div class="form-group">
              <label class="form-label" for="fileReversaDecla">Cargar Archivo <span class="text-danger">*</span></label>
              <input class="form-control" name="fileReversaDecla" id="fileReversaDecla" type="file"
                     accept="image/*,.pdf,.doc,.docx" required>
              <div class="form-text">Formatos permitidos: JPG, PNG, PDF, DOC, DOCX. Tamaño máximo: 5MB</div>
              <div class="invalid-feedback" id="fileReversaDecla_error">
                  Por favor seleccione un archivo válido.
              </div>
          </div>

          <button type="button" class="btn btn-warning" id="reversaDeclaraButton">Declarar entregada</button>
      </div>
  </div>

  <!-- Modal para Rechazo de Material -->
  <div id="popup-container" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1050; align-items: center; justify-content: center;">
      <div id="popup" style="background: white; padding: 20px; border-radius: 8px; max-width: 400px; width: 90%; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <h2 style="margin-bottom: 20px; color: #d63384;">Gestión de rechazo</h2>
          
          <input type="hidden" id="popupSerial">
          <input type="hidden" id="popupTicket">
          <input type="hidden" id="popupIdTecnicoDestino">
          <input type="hidden" id="popupAccion">
          
          <div style="margin-bottom: 15px;">
              <label for="motivoRechazo" style="display: block; margin-bottom: 5px; font-weight: bold;">Motivo del rechazo:</label>
              <select id="motivoRechazo" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" required>
                  <option value="" selected disabled>Seleccione un motivo</option>
                  <option value="Serie fisica no entregada">Serie física no entregada</option>
                  <option value="Serie no corresponde">Serie no corresponde</option>
              </select>
          </div>
          
          <div style="text-align: center; margin-top: 20px;">
              <button onclick="Rechazoaceptar()" style="background-color: #d63384; color: white; border: none; padding: 10px 20px; margin-right: 10px; border-radius: 4px; cursor: pointer;">Aceptar</button>
              <button onclick="Rechazocancelar()" style="background-color: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Cancelar</button>
          </div>
      </div>
  </div>

  <!-- Configuración global para módulo de logística -->
  <script>
    // Asegurar que el título sea correcto
    document.title = "Módulo Logística";
    
    // Configuración global para el módulo de logística
    window.ModLogisticaConfig = {
      endpoints: {
        main: 'GET_LOGISTICA.php',
        historial: 'GET_LOGISTICA.php',
        tecnicos: 'GET_LOGISTICA.php?accion=lista_tecnicos',
        precio: 'GET_LOGISTICA.php',
        actualizarTabla: 'GET_LOGISTICA.php'
      },
      user: {
        id: <?php echo json_encode($id_usuario); ?>,
        nombre: <?php echo json_encode($nombre_usuario); ?>,
        rut: <?php echo json_encode($rut_usuario); ?>,
        nombreShort: <?php echo json_encode($nombre_short); ?>,
        area: <?php echo json_encode($area_usuario); ?>,
        perfil: <?php echo json_encode($perfil_usuario); ?>
      },
      debug: <?php echo json_encode(true); ?> // Para desarrollo
    };
  </script>

  <!-- Scripts -->
  <script>
    // Declarar variable global userId para los scripts (solo si no está ya declarada)
    if (typeof userId === 'undefined') {
      var userId = <?php echo $id_usuario; ?>;
    }
  </script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Módulos de logística (cargados en orden de dependencias) -->
  <!-- COMENTADO HASTA QUE SE SUBAN LOS ARCHIVOS AL SERVIDOR DE PRODUCCIÓN -->
  <!--
  <script src="js/mod_logistica/config.js" defer></script>
  <script src="js/mod_logistica/validation.js" defer></script>
  <script src="js/mod_logistica/ajax-handlers.js" defer></script>
  <script src="js/mod_logistica/form-handlers.js" defer></script>
  <script src="js/mod_logistica/table-handlers.js" defer></script>
  <script src="js/mod_logistica/mod_logistica.js" defer></script>
  -->
  <script src="js/activity_dashboard.js"></script>
  <script src="js/collapsible-sections.js"></script>
  <script src="js/core/footer_modular.js"></script>
  
  <!-- Sistema de Lazy Loading v2 - TEMPORALMENTE COMENTADO DEBIDO A ERRORES 404 -->
  <!--
  <script src="js/lazy-loading-enhanced.js"></script>
  <script src="js/export-tables.js"></script>
  <script src="js/table-config.js"></script>


  <script src="js/stagewise-init.js"></script>


  <script src="js/historial_loader.js"></script>


  <script src="js/lazy_loading_compatibility.js"></script>
  -->

  <!-- COMENTADO TEMPORALMENTE PARA EVITAR CONFLICTOS CON EL SISTEMA DE LAZY LOADING -->
  <!-- <script src="js/mod_logistica_tables.js"></script> -->

  <!-- Script personalizado para mejorar el comportamiento del botón aceptar -->
  <script src="js/mod_logistica_custom.js"></script>

  
  
  <!-- El JavaScript para navegación por pestañas ahora está en los módulos -->

  <!-- Todo el JavaScript ahora está modularizado en archivos separados -->




  <!-- Módulos JavaScript del Sistema de Logística -->
  <!-- Cargar módulos en orden de dependencias -->
  <script src="js/mod_logistica/config.js"></script>
  <script src="js/mod_logistica/api.js"></script>
  <script src="js/mod_logistica/ui.js"></script>
  <script src="js/mod_logistica/tables.js"></script>
  <script src="js/mod_logistica/handlers.js"></script>
  <script src="js/mod_logistica/main.js"></script>

  <!-- Script de compatibilidad para mantener funciones globales -->
  <script>
    console.log('🚀 Sistema Modular de Logística iniciado');

    // Los módulos se han cargado por separado para mejor organización y mantenimiento

    // Estado de carga de las tablas
    const loadingState = {
      directa: false,
      faltante: false,
      recepcion: false,
      reversa: false
    };

    // ID del usuario (obtenido desde PHP)
    // Usar la variable userId ya declarada globalmente

    // Función para cargar datos de una tabla específica
    async function loadTableData(tableName) {
      // Verificar si ya está cargando
      if (loadingState[tableName]) {
        console.log(`⏳ Tabla ${tableName} ya se está cargando...`);
        return;
      }

      // Verificar si ya está en cache
      if (tableCache[tableName]) {
        console.log(`📋 Usando datos en cache para tabla ${tableName}`);
        renderTableData(tableName, tableCache[tableName]);
        return;
      }

      // Marcar como cargando
      loadingState[tableName] = true;

      // Mostrar indicador de carga en el botón
      const tabButton = document.querySelector(`[data-tab="${tableName}"]`);
      if (tabButton) {
        tabButton.classList.add('loading');
      }

      // Mostrar spinner en la tabla
      showTableLoading(tableName);

      try {
        // Preparar datos para envío POST
        const postData = new URLSearchParams();
        postData.append('tabla', tableName);
        postData.append('id_usuario', userId);

        // Usar el archivo real de carga de datos
        const response = await fetch('api_mod_logis_load_table.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: postData
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Obtener el texto de la respuesta
        const responseText = await response.text();

        // Intentar limpiar la respuesta para extraer solo el JSON válido
        let cleanedResponse = responseText.trim();
        
        // Buscar el primer { y el último } para extraer solo el JSON
        const firstBrace = cleanedResponse.indexOf('{');
        const lastBrace = cleanedResponse.lastIndexOf('}');
        
        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
          cleanedResponse = cleanedResponse.substring(firstBrace, lastBrace + 1);
        }

        // Intentar parsear como JSON
        let data;
        try {
          data = JSON.parse(cleanedResponse);
        } catch (parseError) {
          console.error('❌ Error parseando JSON:', parseError);
          throw new Error(`Error parseando JSON: ${parseError.message}.`);
        }

        if (data.success) {
          // Guardar en cache
          tableCache[tableName] = data.datos;

          // Renderizar datos
          renderTableData(tableName, data.datos);

          // Marcar como cargado
          if (tabButton) {
            tabButton.classList.remove('loading');
            tabButton.classList.add('loaded');
          }
        } else {
          throw new Error(data.error || 'Error desconocido');
        }

      } catch (error) {
        console.error(`❌ Error cargando tabla ${tableName}:`, error);
        showTableError(tableName, error.message);

        // Remover indicador de carga
        if (tabButton) {
          tabButton.classList.remove('loading');
        }
      } finally {
        loadingState[tableName] = false;
      }
    }

    // Función para mostrar indicador de carga en la tabla
    function showTableLoading(tableName) {
      const tableBody = document.getElementById(`${tableName}TableBody`);
      if (tableBody) {
        tableBody.innerHTML = `
          <tr class="loading-row">
            <td colspan="3" class="text-center">
              <div class="loading-container" style="color: #00e1fd; padding: 20px;">
                <div class="spinner-border spinner-border-sm me-2" role="status" style="color: #00e1fd; border-color: #00e1fd transparent #00e1fd transparent;">
                  <span class="visually-hidden">Cargando...</span>
                </div>
                <span style="color: #00e1fd; font-weight: 500;">Cargando datos de ${tableName}...</span>
              </div>
            </td>
          </tr>
        `;
      }
    }

    // Función para mostrar error en la tabla
    function showTableError(tableName, errorMessage) {
      const tableBody = document.getElementById(`${tableName}TableBody`);
      if (tableBody) {
        tableBody.innerHTML = `
          <tr class="error-row">
            <td colspan="3" class="text-center text-danger">
              <div class="error-container">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Error al cargar datos: ${errorMessage}
                <br>
                <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadTableData('${tableName}')">
                  <i class="bi bi-arrow-clockwise me-1"></i>
                  Reintentar
                </button>
              </div>
            </td>
          </tr>
        `;
      }
    }

    // Funciones para manejo de pestañas con lazy loading
    // Función para invalidar cache de una tabla específica
    function invalidateTableCache(tableName) {
      if (tableCache[tableName]) {
        console.log(`🗑️ Invalidando cache para tabla ${tableName}`);
        tableCache[tableName] = null;
        loadingState[tableName] = false;
        
        const tabButton = document.querySelector(`[data-tab="${tableName}"]`);
        if (tabButton) {
          tabButton.classList.remove('loaded');
        }
      }
    }
    
    function initTabSystem() {
      const tabButtons = document.querySelectorAll('.tab-button');
      const tableSections = document.querySelectorAll('.table-section');

      // Asegurar que la pestaña recepción esté activa por defecto
      tabButtons.forEach(btn => btn.classList.remove('active'));
      const recepcionTab = document.querySelector('[data-tab="recepcion"]');
      if (recepcionTab) {
        recepcionTab.classList.add('active');
      }

      // Asegurar que solo la tabla recepción esté visible
      tableSections.forEach(section => {
        section.style.setProperty('display', 'none', 'important');
      });
      const recepcionSection = document.querySelector('[data-table="recepcion"]');
      if (recepcionSection) {
        recepcionSection.style.setProperty('display', 'block', 'important');
      }

      tabButtons.forEach(button => {
        button.addEventListener('click', function() {
          const tabName = this.getAttribute('data-tab');

          // Remover clase active de todos los botones
          tabButtons.forEach(btn => btn.classList.remove('active'));
          // Agregar clase active al botón clickeado
          this.classList.add('active');

          // PRIMERO: Ocultar TODAS las secciones de tabla con !important
          tableSections.forEach(section => {
            section.style.setProperty('display', 'none', 'important');
          });

          // SEGUNDO: Mostrar SOLO la sección correspondiente con !important
          const targetSection = document.querySelector(`[data-table="${tabName}"]`);
          if (targetSection) {
            targetSection.style.setProperty('display', 'block', 'important');
          }
          
          // Si es la tabla de reversa, invalidar el cache para forzar recarga
          // ya que podría haber recibido actualizaciones mientras estaba en segundo plano
          if (tabName === 'reversa') {
            invalidateTableCache('reversa');
          }

          // TERCERO: Cargar datos de la tabla si no están cargados
          loadTableData(tabName);
        });
      });
    }

    // Función para renderizar datos en la tabla
    function renderTableData(tableName, datos) {
      const tableBody = document.getElementById(`${tableName}TableBody`);
      if (!tableBody) {
        console.error(`❌ No se encontró el tbody para la tabla ${tableName}`);
        return;
      }

      if (!datos || datos.length === 0) {
        tableBody.innerHTML = `
          <tr>
            <td colspan="3" class="text-center text-muted">
              <i class="bi bi-inbox me-2"></i>
              No hay datos disponibles para ${tableName}
            </td>
          </tr>
        `;
        return;
      }

      let html = '';
      datos.forEach(fila => {
        // Determinar el estado y la clase CSS correspondiente
        let estado = 'Pendiente';
        let clase_estado = 'pendiente';

        // Lógica específica por tabla
        switch (tableName) {
          case 'directa':
            estado = fila.Semantica || 'Pendiente';
            break;
          case 'faltante':
            estado = fila.Semantica || 'Faltante';
            clase_estado = 'faltante';
            break;
          case 'recepcion':
            estado = fila.tipo_movimiento || 'Pendiente por recibir';
            clase_estado = 'recibido';
            break;
          case 'reversa':
            estado = fila.Semantica || 'Reversa';
            clase_estado = 'reversa';
            break;
        }

        // Asignar clase CSS basada en el estado
        // 1. Estados de rechazo - rojo con texto blanco
        // 2. Estado disponible - verde con texto blanco
        // 3. Todos los demás - amarillo con texto negro
        
        // Verificar casos específicos para aplicar clases personalizadas
        const estadoLower = estado.toLowerCase();
        
        // Caso especial para Rechaza bodega - naranja oscuro
        if (estadoLower === 'rechaza bodega' || estadoLower === 'rechaza bodega justificación') {
          clase_estado = 'rechaza-bodega';
        }
        // Verificar si es un estado de rechazo (contiene la palabra 'rechaza')
        else if (estadoLower.includes('rechaza')) {
          clase_estado = 'rechazo';
        }
        // Verificar casos específicos de rechazo por ID conocidos
        else if (
          estadoLower === 'faltante' ||
          estadoLower.includes('rechazo') ||
          estadoLower === 'rechaza tecnico' ||
          estadoLower === 'rechaza supervisor' ||
          estadoLower === 'rechaza supervisor reversa'
        ) {
          clase_estado = 'rechazo';
        }
        // Estado disponible - verde con texto blanco
        else if (estado.toLowerCase() === 'disponible') {
          clase_estado = 'disponible';
        }
        // Todos los demás estados - amarillo con texto negro
        else {
          clase_estado = 'default';
        }

        // Generar la fila HTML - Botones específicos para cada tabla
        const serialNumber = fila.Serial || fila.serie || 'N/A';
        
        if (tableName === 'recepcion') {
          // Calcular variableCondicion como en el original
          const idMovimiento = fila.id_movimiento || '';
          const idTecnicoDest = fila.id_tecnico_destino || fila.id_transfer || '';
          const variableCondicion = (idMovimiento == 15) ? "Si" : idTecnicoDest;
          
          html += `
            <tr data-type="${tableName}" data-serial="${serialNumber}">
              <td>${serialNumber}</td>
              <td><span class="status-badge ${clase_estado}">${estado}</span></td>
              <td>
                <div class="action-buttons-container">
                  <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serialNumber}">
                    <i class="bi bi-clock-history"></i>
                  </button>
                  <button class="action-btn aceptar-btn btn btn-success" title="Aceptar Material" 
                          onclick="actualizarRegistro('${serialNumber}', '${variableCondicion}', '${idMovimiento}', 'ACEPTA')">
                    <i class="bi bi-check-circle"></i>
                  </button>
                  <button class="action-btn justificar-btn" title="Rechazar Material" 
                          onclick="rechazoMaterial('${serialNumber}', '${fila.id_tecnico_origen || fila.ticket || ''}', '${idTecnicoDest}', 'RECHAZA')">
                    <i class="bi bi-journal-x"></i>
                  </button>
                </div>
              </td>
            </tr>
          `;
        } else {
          // Definir botones para todas las tablas
          let actionButtonsHtml = ``;
          
          // Configuración especial para la tabla directa
          if (tableName === 'directa') {
            actionButtonsHtml = `
              <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serialNumber}">
                <i class="bi bi-clock-history"></i>
              </button>
              <button class="action-btn declarar-btn" title="Declarar" data-serie="${serialNumber}" data-id-movimiento="${fila.id_movimiento || '1'}">
                <i class="bi bi-clipboard-check"></i>
              </button>
              <button class="action-btn justificar-btn transfiere-btn" title="Transferir" data-serie="${serialNumber}" 
                      onclick="redirigirEnTransferencia('${serialNumber}', '${fila.Item || serialNumber}', '${fila.id_movimiento || '1'}', 'TRANSFIERE')">
                <i class="bi bi-send-fill"></i>
              </button>
            `;
          } else if (tableName === 'reversa') {
            // Botones específicos para la tabla de reversa
            actionButtonsHtml = `
              <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serialNumber}">
                <i class="bi bi-clock-history"></i>
              </button>
              <button class="action-btn declarar-rev-btn" title="Declarar Entrega" data-serie="${serialNumber}" data-id-movimiento="${fila.id_movimiento || '0'}">
                <i class="bi bi-box-seam"></i>
              </button>
              <button class="action-btn transferir-rev-btn" title="A Supervisor" data-serie="${serialNumber}" data-id-movimiento="${fila.id_movimiento || '0'}">
                <i class="bi bi-send"></i>
              </button>
            `;
          } else {
            // Botón estándar para otras tablas
            actionButtonsHtml = `
              <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serialNumber}">
                <i class="bi bi-clock-history"></i>
              </button>
              <button class="action-btn declarar-btn" title="Declarar" data-serie="${serialNumber}" data-id-movimiento="${fila.id_movimiento || '1'}">
                <i class="bi bi-clipboard-check"></i>
              </button>
              <button class="action-btn justificar-btn" title="Justificar" data-serie="${serialNumber}">
                <i class="bi bi-shield-check"></i>
              </button>
            `;
          }
          
          html += `
            <tr data-type="${tableName}" data-serial="${serialNumber}">
              <td>${serialNumber}</td>
              <td><span class="status-badge ${clase_estado}">${estado}</span></td>
              <td>
                <div class="action-buttons-container">
                  ${actionButtonsHtml}
                </div>
              </td>
            </tr>
          `;
        }
      });

      tableBody.innerHTML = html;

      // Reinicializar event listeners para los botones de acción
      setupActionButtons();
    }

    // Función para configurar botones de acción
    function setupActionButtons() {
      // Botones de historial
      const historialButtons = document.querySelectorAll('.historial-btn');
      
      historialButtons.forEach((button) => {
        // Remover listeners previos
        button.removeEventListener('click', handleHistorialClick);
        // Agregar nuevo listener
        button.addEventListener('click', handleHistorialClick);
      });

      // Botones de declarar
      const declararButtons = document.querySelectorAll('.declarar-btn');
      declararButtons.forEach(button => {
        button.removeEventListener('click', handleDeclararClick);
        button.addEventListener('click', handleDeclararClick);
      });

      // Botones de justificar (excluyendo los botones de rechazo)
      const justificarButtons = document.querySelectorAll('.justificar-btn:not([onclick*="rechazoMaterial"])');
      justificarButtons.forEach(button => {
        button.removeEventListener('click', handleJustificarClick);
        button.addEventListener('click', handleJustificarClick);
      });
      
      // Botones de transferir (que no tengan onclick)
      const transfiereButtons = document.querySelectorAll('.transfiere-btn:not([onclick])');
      transfiereButtons.forEach(button => {
        button.removeEventListener('click', handleTransfiereClick);
        button.addEventListener('click', handleTransfiereClick);
      });
    }

    // Handler para botón historial
    function handleHistorialClick(e) {
      e.preventDefault();
      
      const serie = e.currentTarget.getAttribute('data-serie');

      if (!serie) {
        console.error('❌ No se pudo obtener la serie del botón');
        return;
      }

      // Llamar a la función de historial del LogisticaTablesSystem si está disponible
      if (window.LogisticaTablesSystem && window.LogisticaTablesSystem.mostrarHistorial) {
        window.LogisticaTablesSystem.mostrarHistorial({
          serie: serie,
          tipo: 'historial',
          estado: 'activo'
        });
      } else {
        // Abrir offcanvas directamente
        const offcanvasElement = document.getElementById('offcanvasHistorial');
        const serieInput = document.getElementById('serieHistorial');
        
        if (offcanvasElement && serieInput) {
          serieInput.value = serie;
          const historialOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
          historialOffcanvas.show();
          
          // Cargar historial después de mostrar el offcanvas
          setTimeout(() => {
            cargarHistorialDirecto(serie);
          }, 300);
        } else {
          console.error('❌ No se encontraron elementos del offcanvas de historial');
        }
      }
    }

    // Handler para botón declarar - Replicando funcionalidad de Tecnico_Home_LOGIS_TEST_V2.php
    function handleDeclararClick(e) {
      e.preventDefault();
      const button = e.currentTarget;
      const serie = button.getAttribute('data-serie');
      const idMovimiento = button.getAttribute('data-id-movimiento') || '1';
      
      // Ejecutar función redirigirEnTransferencia replicada de Tecnico_Home
      redirigirEnTransferencia(serie, serie, idMovimiento, 'INSTALA');
    }

    // Función redirigirEnTransferencia replicada de Tecnico_Home_LOGIS_TEST_V2.php
    function redirigirEnTransferencia(serial, item, ID_MOVI, accion) {
      console.log('🔄 redirigirEnTransferencia LLAMADA:', { serial, item, ID_MOVI, accion });
      
      // Validaciones comunes para INSTALA y TRANSFIERE
      function validarEstado() {
        if (ID_MOVI === '5') {
          console.log('⚠️ Estado 5: Ya declarado como instalado');
          mostrarNotificacion('Serial ya fue declarado como instalado', 'warning');
          return false;
        } else if (ID_MOVI === '3') {
          console.log('⚠️ Estado 3: Pendiente por el usuario');
          mostrarNotificacion('Aun pendiente por el usuario a quien escalaste el material', 'warning');
          return false;
        } else if (ID_MOVI === '4') {
          console.log('⚠️ Estado 4: Justificado por supervisor');
          mostrarNotificacion('El material ya fue justificado por el supervisor', 'warning');
          return false;
        } else if (ID_MOVI === '13') {
          console.log('⚠️ Estado 13: Escalado a VTR');
          mostrarNotificacion('El material ha sido escalado a VTR', 'warning');
          return false;
        }
        return true;
      }

      // Función auxiliar para preparar y mostrar offcanvas
      function prepararOffcanvas(targetId, inputId, serial) {
        try {
          // Asegurarnos de que el body esté en un estado consistente
          document.body.classList.remove('modal-open');
          document.body.style.overflow = '';
          document.body.style.paddingRight = '';
          
          // Eliminar cualquier backdrop existente antes de crear uno nuevo
          const backdropElements = document.querySelectorAll('.offcanvas-backdrop');
          backdropElements.forEach(element => {
            element.remove();
          });
          
          // Cerrar cualquier otro offcanvas abierto
          document.querySelectorAll('.offcanvas.show').forEach(offcanvasEl => {
            const instance = bootstrap.Offcanvas.getInstance(offcanvasEl);
            if (instance) {
              try {
                instance.hide();
              } catch (e) {
                console.warn('Error al ocultar offcanvas:', e);
              }
            }
          });
          
          // Asignar valor al campo de serie
          const serieInput = document.getElementById(inputId);
          if (serieInput) {
            serieInput.value = serial;
            console.log('🔍 Valor de ' + inputId + ' establecido:', serial);
          } else {
            console.error('No se encontró el campo ' + inputId);
          }
          
          const offcanvasElement = document.getElementById(targetId);
          console.log('🔍 Elemento offcanvas encontrado:', !!offcanvasElement);
          
          if (!offcanvasElement) {
            throw new Error('No se encontró el elemento offcanvas ' + targetId);
          }
          
          // Asegurarse de que tiene el atributo data-bs-backdrop="true"
          offcanvasElement.setAttribute('data-bs-backdrop', 'true');
          
          // Asegurarse de que el offcanvas no esté en estado show
          offcanvasElement.classList.remove('show');
          
          // Esperar un breve momento para asegurar que cualquier instancia previa se haya limpiado
          setTimeout(() => {
            try {
              // Crear el objeto offcanvas con opciones explícitas
              const offcanvas = new bootstrap.Offcanvas(offcanvasElement, {
                backdrop: true,
                scroll: false
              });
              
              // Mostrar el offcanvas
              offcanvas.show();
              console.log('🔓 Offcanvas ' + targetId + ' abierto');
            } catch (innerError) {
              console.error('Error al mostrar offcanvas:', innerError);
              mostrarNotificacion('Ocurrió un error al mostrar el formulario', 'danger');
            }
          }, 50);
        } catch (error) {
          console.error('Error general al procesar acción:', error);
          mostrarNotificacion('Error al mostrar el formulario', 'danger');
        }
      }
      
      if (accion === 'INSTALA') {
        console.log('🏠 Procesando instalación');
        // Validar estado del material
        if (!validarEstado()) return;
        prepararOffcanvas('offcanvasInstala', 'serie_insta', serial);
      } 
      else if (accion === 'TRANSFIERE') {
        console.log('📤 Procesando transferencia');
        // Validar estado del material
        if (!validarEstado()) return;
        prepararOffcanvas('offcanvasrigh', 'serie_tran', serial);
        
        // Configurar los botones después de mostrar el offcanvas
        setTimeout(() => {
          if (typeof setupOffcanvasButtons === 'function') {
            setupOffcanvasButtons();
          }
          console.log('🔍 Serie activa para acciones:', serial);
        }, 150);
      }
      // Manejar acciones específicas para la tabla de reversa
      else if (accion === 'ENTREGA_REV') {
        console.log('📦 Procesando entrega reversa');
        // Preparar y mostrar el offcanvas de entrega reversa
        prepararOffcanvas('offcanvasReversaDeclara', 'serieReversaDeclara', serial);
        
        // Configurar el botón de declarar entregada
        const reversaDeclaraButton = document.getElementById('reversaDeclaraButton');
        if (reversaDeclaraButton) {
          // Remover listeners previos para evitar duplicados
          reversaDeclaraButton.removeEventListener('click', handleReversaDeclaraClick);
          reversaDeclaraButton.addEventListener('click', handleReversaDeclaraClick);
        }
      }
      else if (accion === 'TRANSFIERE_REV') {
        console.log('📤 Procesando transferencia reversa');
        // Preparar y mostrar el offcanvas de transferencia reversa
        prepararOffcanvas('offcanvasrevSuper', 'serie_trans_rever', serial);
        
        // Configurar el botón de solicitar requerimiento
        const transferReversaButton = document.getElementById('transferReversaButton');
        if (transferReversaButton) {
          // Remover listeners previos para evitar duplicados
          transferReversaButton.removeEventListener('click', handleTransferReversaClick);
          transferReversaButton.addEventListener('click', handleTransferReversaClick);
        }
        
        // Cargar automáticamente el supervisor asignado
        const superDestinoInput = document.getElementById('super_destino');
        if (superDestinoInput) {
          // Obtener el ID del supervisor asociado al usuario actual
          const supervisorId = <?php echo isset($id_supervisor) && $id_supervisor ? $id_supervisor : 'null'; ?>;
          if (supervisorId) {
            superDestinoInput.value = supervisorId;
            console.log('🔍 Supervisor asignado automáticamente:', supervisorId);
          } else {
            console.warn('⚠️ No se encontró supervisor asociado al usuario');
            mostrarNotificacion('No se encontró supervisor asociado. Contacte a soporte.', 'warning');
          }
        }
      }
    }
    
    // Función para procesar la transferencia
    function transferirRegistro(Serial, ticket, accion, id_tecnico_destino, motivo) {
      // Verificar que la serie sea la correcta
      const serieEnFormulario = document.getElementById('serie_tran').value;
      const serieCoincide = serieEnFormulario === Serial;
      
      // Log con estilo para destacar en la consola
      console.log('%c📤 INICIANDO TRANSFERENCIA', 'background: #4CAF50; color: white; font-size: 12px; padding: 5px; border-radius: 5px;');
      console.table({
        'Serial': Serial,
        'Serie en formulario': serieEnFormulario,
        'Coinciden': serieCoincide ? 'Sí ✓' : 'No ✗',
        'Ticket': ticket,
        'Acción': accion,
        'Técnico Destino': id_tecnico_destino,
        'Motivo': motivo
      });
      
      // Forzar que los mensajes aparezcan
      console.debug('Asegurando que los logs sean visibles');
      
      // Validar los parámetros obligatorios
      if (!Serial || !accion || !motivo) {
        console.error('%c❌ ERROR DE VALIDACIÓN:', 'background: #F44336; color: white; font-size: 12px; padding: 5px; border-radius: 5px;', 
                     { Serial, accion, motivo });
        alert('Error: Faltan datos para completar la transferencia');
        return;
      }
      
      // Advertir si las series no coinciden (para debug)
      if (!serieCoincide) {
        console.warn('%c⚠️ ADVERTENCIA: La serie pasada al método no coincide con el valor en el formulario', 
                    'background: #FFC107; color: black; font-size: 12px; padding: 5px; border-radius: 5px;');
      }
      
      // Obtener ID del técnico origen de forma segura
      const id_tecnico_origen = window.ModLogisticaConfig && window.ModLogisticaConfig.user ? 
                               window.ModLogisticaConfig.user.id : '0';
      
      console.log('%c👤 ORIGEN:', 'font-weight: bold;', 'Técnico ID:', id_tecnico_origen);
      console.log('%c👤 DESTINO:', 'font-weight: bold;', 'Técnico ID:', id_tecnico_destino);
      
      // Crear un objeto FormData para enviar los datos
      var formData = new FormData();
      
      // Agregar los datos al FormData en el orden correcto que espera GET_LOGIS_DIRECTA.php
      formData.append('Serial', Serial);
      formData.append('accion', accion);  // "transfiere"
      formData.append('id_tecnico_origen', id_tecnico_origen);
      formData.append('id_tecnico_destino', id_tecnico_destino);
      formData.append('ticket', ticket || 'Si');
      formData.append('motivo', motivo);
      
      // Si hay un archivo adjunto, agregarlo
      const fileInput = document.getElementById('fileInventario');
      if (fileInput && fileInput.files.length > 0) {
        formData.append('fileInventario', fileInput.files[0]);
      }
      
      // Depurar el contenido completo del FormData
      console.log('%c📝 DATOS A ENVIAR:', 'background: #2196F3; color: white; font-size: 12px; padding: 5px; border-radius: 5px;');
      
      // Crear una tabla para mostrar los datos de forma más clara
      const formDataObj = {};
      formData.forEach(function(value, key) {
        formDataObj[key] = value;
        // Log individual para asegurar visibilidad
        console.log(`- ${key}: ${value}`);
      });
      console.table(formDataObj);
      
      // Mostrar indicador de carga
      const transferButton = document.getElementById('transferButton');
      if (transferButton) {
        transferButton.disabled = true;
        transferButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Enviando...';
      }
      
      // Imprimir mensaje para indicar el inicio de la solicitud
      console.log('%c💬 ENVIANDO SOLICITUD...', 'background: #FF9800; color: white; font-size: 12px; padding: 5px; border-radius: 5px;');
      
      // Realizar la solicitud POST utilizando XMLHttpRequest (igual que en Tecnico_Home_LOGIS_TEST2.php)
      var request = new XMLHttpRequest();
      
      // Monitorear todos los estados de la solicitud
      request.onreadystatechange = function() {
        console.log('%c💹 ESTADO DE LA SOLICITUD:', 'font-weight: bold;', 'ReadyState:', request.readyState, 'Status:', request.status);
      };
      
      // Abrir la solicitud
      request.open('POST', 'GET_LOGIS_DIRECTA.php');
      
      // Configurar manejo de respuesta
      request.onload = function() {
        // Procesar la respuesta del servidor
        if (request.status === 200) {
          console.log('%c✅ RESPUESTA EXITOSA:', 'background: #4CAF50; color: white; font-size: 12px; padding: 5px; border-radius: 5px;');
          console.log('Respuesta completa:', request.responseText);
          
          try {
            // Intentar parsear como JSON por si acaso
            const jsonResponse = JSON.parse(request.responseText);
            console.log('Respuesta como JSON:', jsonResponse);
          } catch (e) {
            // Si no es JSON, mostrar como texto
            console.log('Respuesta como texto:', request.responseText);
          }
          
          // Cerrar el offcanvas
          const offcanvasElement = document.getElementById('offcanvasrigh');
          if (offcanvasElement) {
            const offcanvasBS = bootstrap.Offcanvas.getInstance(offcanvasElement);
            if (offcanvasBS) {
              offcanvasBS.hide();
            }
          }
          
          // Mostrar mensaje de éxito
          alert('Transferencia realizada con éxito');
          
          // Recargar la tabla correspondiente
          if (typeof loadTableData === 'function') {
            // Determinar qué tabla recargar basado en dónde se originó la transferencia
            const activeTab = document.querySelector('.tab-button.active');
            if (activeTab) {
              const tabName = activeTab.getAttribute('data-tab');
              if (tabName) {
                console.log('%c🔄 RECARGANDO TABLA:', 'font-weight: bold;', tabName);
                loadTableData(tabName);
              }
            }
          }
        } else {
          console.error('%c❌ ERROR EN LA SOLICITUD:', 'background: #F44336; color: white; font-size: 12px; padding: 5px; border-radius: 5px;');
          console.error('Status:', request.status);
          console.error('StatusText:', request.statusText);
          console.error('Respuesta:', request.responseText);
          
          alert('Error al realizar la transferencia. Consulta la consola para más detalles.');
        }
        
        // Restaurar el botón
        if (transferButton) {
          transferButton.disabled = false;
          transferButton.innerHTML = 'Enviar requerimiento';
        }
      };
      
      // Configurar manejo de errores
      request.onerror = function(e) {
        console.error('%c❌ ERROR DE RED:', 'background: #F44336; color: white; font-size: 12px; padding: 5px; border-radius: 5px;');
        console.error('Detalles del error:', e);
        
        alert('Error de conexión al realizar la transferencia. Verifica tu conexión a internet.');
        
        // Restaurar el botón
        if (transferButton) {
          transferButton.disabled = false;
          transferButton.innerHTML = 'Enviar requerimiento';
        }
      };
      
      // Enviar la solicitud con los datos del formulario
      try {
        request.send(formData);
        console.log('%c📣 SOLICITUD ENVIADA', 'background: #9C27B0; color: white; font-size: 12px; padding: 5px; border-radius: 5px;');
      } catch (e) {
        console.error('%c❌ ERROR AL ENVIAR:', 'background: #F44336; color: white; font-size: 12px; padding: 5px; border-radius: 5px;', e);
      }
    }

    // Handler para botón justificar - en tablas distintas a directa justifica, en directa transfiere
    function handleJustificarClick(e) {
      e.preventDefault();
      const serie = e.currentTarget.getAttribute('data-serie');
      const tableType = e.currentTarget.closest('tr').getAttribute('data-type');
      
      console.log('🔍 Botón justificar/transferir clickeado:', {
        serie: serie,
        tableType: tableType,
        elemento: e.currentTarget,
        clases: e.currentTarget.className
      });
      
      if (tableType === 'directa') {
        // Para directa, usamos el botón justificar como transfiere
        const idMovimiento = e.currentTarget.getAttribute('data-id-movimiento') || '1';
        console.log('🔑 Ejecutando transferencia para serie:', serie, 'ID_MOVI:', idMovimiento);
        redirigirEnTransferencia(serie, serie, idMovimiento, 'TRANSFIERE');
      } else {
        // Para otras tablas, comportamiento normal de justificar
        console.log('📝 Mostrando formulario de justificación para serie:', serie);
        try {
          const offcanvasElement = document.getElementById('offcanvasrigh');
          console.log('🔍 Elemento offcanvas encontrado:', !!offcanvasElement);
          
          // Asignar el valor de serie al campo
          const serieField = document.getElementById('serie_tran');
          console.log('🔍 Campo serie_tran encontrado:', !!serieField);
          if (serieField) serieField.value = serie;
          
          // Crear el objeto offcanvas
          const offcanvas = new bootstrap.Offcanvas(offcanvasElement);
          console.log('🔍 Objeto offcanvas creado');
          
          // Agregar manejadores de eventos para los botones del offcanvas
          setupOffcanvasButtons();
          
          // Mostrar el offcanvas
          offcanvas.show();
          console.log('🔓 Offcanvas mostrado');
        } catch (error) {
          console.error('❌ Error al mostrar offcanvas:', error);
          alert('Error al mostrar el formulario de justificación');
        }
      }
    }
    
    // Handler para botón transferir
    function handleTransfiereClick(e) {
      e.preventDefault();
      const serie = e.currentTarget.getAttribute('data-serie');
      const idMovimiento = e.currentTarget.getAttribute('data-id-movimiento') || '1';
      
      // Usar redirigirEnTransferencia para mantener la misma lógica
      redirigirEnTransferencia(serie, serie, idMovimiento, 'TRANSFIERE');
    }

    // Función para cargar historial directamente
    function cargarHistorialDirecto(serie) {
      const webHistorial = document.getElementById('webHistorial');
      const precioInput = document.getElementById('precioHistorial');
      
      if (!webHistorial) {
        console.error('❌ No se encontró el contenedor webHistorial');
        return;
      }

      // Mostrar indicador de carga
      webHistorial.innerHTML = `
        <div class="spinner-container-center">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">Cargando historial...</span>
          </div>
          <p>Cargando historial...</p>
        </div>
      `;

      // Obtener precio primero
      if (precioInput) {
        fetch(`GET_LOGISTICA.php?proceso=precio&serie=${serie}`)
          .then(response => response.json())
          .then(data => {
            if (data.precio) {
              const precioFormateado = new Intl.NumberFormat('es-CL', {
                style: 'currency',
                currency: 'CLP'
              }).format(data.precio);
              precioInput.value = precioFormateado;
            } else {
              precioInput.value = 'Precio no disponible';
            }
          })
          .catch(error => {
            console.error('❌ Error obteniendo precio:', error);
            precioInput.value = 'Error al obtener precio';
          });
      }

      // Obtener historial
      fetch(`GET_LOGISTICA.php?proceso=historial2&serie=${serie}`)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          return response.json();
        })
        .then(data => {
          
          if (!Array.isArray(data)) {
            throw new Error('Formato de datos incorrecto');
          }

          if (data.length === 0) {
            webHistorial.innerHTML = '<div class="alert alert-info p-3">No se encontraron registros de historial para esta serie</div>';
            return;
          }

          let html = '';
          data.forEach((item, index) => {
            html += `
              <div class="card-item mb-3">
                <div class="card-content p-3 border rounded">
                  <div class="card-date text-primary fw-bold mb-2">📅 Fecha: ${item.fecha_hora || 'Sin fecha'}</div>
                  <div class="card-info mb-1"><strong>🔄 Movimiento:</strong> ${item.Semantica || 'N/A'}</div>
                  <div class="card-info mb-1"><strong>📍 Desde:</strong> ${item.Nombre_origen || 'N/A'}</div>
            `;
            
            if (item.Nombre_destino) {
              html += `<div class="card-info mb-1"><strong>🎯 Hacia:</strong> ${item.Nombre_destino}</div>`;
            }
            if (item.motivo) {
              html += `<div class="card-info mb-1"><strong>💭 Motivo:</strong> ${item.motivo}</div>`;
            }
            if (item.observacion) {
              let textoCorto = item.observacion.substring(0, 50);
              let textoMostrar = (item.observacion.length > 50) ? textoCorto + '...' : textoCorto;
              html += `<div class="card-info mb-1" title="${item.observacion}"><strong>📝 Observación:</strong> ${textoMostrar}</div>`;
            }
            if (item.archivo_adj) {
              html += `<div class="card-info mb-0"><a href="${item.archivo_adj}" class="btn btn-sm btn-outline-primary" download>📎 Descargar respaldo</a></div>`;
            }
            
            html += `</div></div>`;
          });

          webHistorial.innerHTML = html;
        })
        .catch(error => {
          console.error('❌ Error cargando historial:', error);
          webHistorial.innerHTML = `<div class="alert alert-danger p-3">Error al cargar historial: ${error.message}</div>`;
        });
    }

    // Función para búsqueda básica en tablas
    function initSearchSystem() {
      const searchInput = document.getElementById('searchInput');
      const clearButton = document.getElementById('clearSearch');

      if (searchInput) {
        searchInput.addEventListener('input', function() {
          const searchTerm = this.value.toLowerCase();
          const visibleTable = document.querySelector('.table-section[style*="block"]');

          if (visibleTable) {
            const rows = visibleTable.querySelectorAll('tbody tr:not(.loading-row):not(.error-row)');
            rows.forEach(row => {
              const text = row.textContent.toLowerCase();
              if (text.includes(searchTerm)) {
                row.style.display = '';
              } else {
                row.style.display = 'none';
              }
            });
          }
        });
      }

      if (clearButton) {
        clearButton.addEventListener('click', function() {
          searchInput.value = '';
          // Mostrar todas las filas (excepto loading y error)
          const allRows = document.querySelectorAll('.table-section tbody tr:not(.loading-row):not(.error-row)');
          allRows.forEach(row => {
            row.style.display = '';
          });
        });
      }
    }

    // Función para configurar botones del offcanvas - versión optimizada
    function setupOffcanvasButtons() {
      console.log('🔧 Configurando botones del offcanvas');
      
      // Configurar limpieza global de offcanvas si no se ha hecho ya
      if (!window.offcanvasCleanupInitialized) {
        // Función para limpiar después de cerrar un offcanvas
        function cleanupOffcanvas() {
          console.log('🗑️ Limpiando estado de offcanvas');
          
          // Eliminar cualquier backdrop huérfano
          const backdropElements = document.querySelectorAll('.offcanvas-backdrop');
          const activeOffcanvas = document.querySelector('.offcanvas.show');
          
          backdropElements.forEach(el => {
            if (!activeOffcanvas) {
              el.remove();
            }
          });
          
          // Restaurar el estado del body si no hay offcanvas activos
          if (!activeOffcanvas) {
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
          }
        }
        
        // Registrar evento global para limpieza cuando se cierra cualquier offcanvas
        document.body.addEventListener('hidden.bs.offcanvas', function(event) {
          // Ejecutar limpieza después de un breve delay para permitir que Bootstrap termine
          setTimeout(cleanupOffcanvas, 50);
        });
        
        // Marcar como inicializado para no registrar múltiples listeners
        window.offcanvasCleanupInitialized = true;
      }
      
      // Verificar y mostrar la serie actual para debug
      const serieInput = document.getElementById('serie_tran');
      if (serieInput) {
        console.log('🔍 Serie actual en offcanvas:', serieInput.value);
      }
      
      // Configurar botones de offcanvas
      const justificarLink = document.getElementById('justificarLink');
      const bodegaSistemico = document.getElementById('bodegaSistemico');
      const bodegaSeriIncorrecta = document.getElementById('bodegaSeriIncorrecta');
      const bodegaLink = document.getElementById('bodegaLink');
      const tecnicoLink = document.getElementById('tecnicoLink');
      const transferButton = document.getElementById('transferButton');
      
      // Botones nuevos para escalamiento a bodega
      const bodegaLinkTOA = document.getElementById('bodegaLinkTOA');
      const fotoCierreInv = document.getElementById('fotoCierreInv');
      const devueltoBodega = document.getElementById('DevueltoBodega');
      
      // Cargar lista de técnicos para el dropdown
      cargarListaTecnicos();
      
      // Registrar manejadores de eventos para estos botones si existen
      if (justificarLink) {
        console.log('🔧 Configurando justificarLink');
        justificarLink.removeEventListener('click', handleOffcanvasJustificarClick);
        justificarLink.addEventListener('click', handleOffcanvasJustificarClick);
      }
      
      if (bodegaSistemico) {
        console.log('🔧 Configurando bodegaSistemico');
        bodegaSistemico.removeEventListener('click', handleOffcanvasBodegaSistemicoClick);
        bodegaSistemico.addEventListener('click', handleOffcanvasBodegaSistemicoClick);
      }
      
      if (bodegaSeriIncorrecta) {
        console.log('🔧 Configurando bodegaSeriIncorrecta');
        bodegaSeriIncorrecta.removeEventListener('click', handleOffcanvasBodegaSeriIncorrectaClick);
        bodegaSeriIncorrecta.addEventListener('click', handleOffcanvasBodegaSeriIncorrectaClick);
      }
      
      if (bodegaLink) {
        console.log('🔧 Configurando bodegaLink');
        bodegaLink.removeEventListener('click', handleOffcanvasBodegaLinkClick);
        bodegaLink.addEventListener('click', handleOffcanvasBodegaLinkClick);
      }
      
      // Configurar los nuevos botones de escalamiento a bodega
      if (bodegaLinkTOA) {
        console.log('🔧 Configurando bodegaLinkTOA');
        bodegaLinkTOA.removeEventListener('click', handleOffcanvasBodegaTOAClick);
        bodegaLinkTOA.addEventListener('click', handleOffcanvasBodegaTOAClick);
      }
      
      if (fotoCierreInv) {
        console.log('🔧 Configurando fotoCierreInv');
        fotoCierreInv.removeEventListener('click', handleOffcanvasFotoCierreInvClick);
        fotoCierreInv.addEventListener('click', handleOffcanvasFotoCierreInvClick);
      }
      
      if (devueltoBodega) {
        console.log('🔧 Configurando DevueltoBodega');
        devueltoBodega.removeEventListener('click', handleOffcanvasDevueltoBodegaClick);
        devueltoBodega.addEventListener('click', handleOffcanvasDevueltoBodegaClick);
      }
      
      if (tecnicoLink) {
        console.log('🔧 Configurando tecnicoLink');
        tecnicoLink.removeEventListener('click', handleOffcanvasTecnicoLinkClick);
        tecnicoLink.addEventListener('click', handleOffcanvasTecnicoLinkClick);
      }
      
      if (transferButton) {
        console.log('🔧 Configurando transferButton');
        transferButton.removeEventListener('click', handleTransferButtonClick);
        transferButton.addEventListener('click', handleTransferButtonClick);
      }
    }
    
    // Manejar clic en "Transferencia a otro tecnico"
    function handleOffcanvasTecnicoLinkClick(e) {
      e.preventDefault();
      console.log('👆 Clic en tecnicoLink para transferencia a otro técnico');
      
      // Mostrar selector de técnico y resetear valores
      const usuarioDestinoInput = document.getElementById('usuario_destino');
      if (usuarioDestinoInput) {
        usuarioDestinoInput.value = '';
        usuarioDestinoInput.disabled = false;
      }
      
      // Asegurarse de que la lista de técnicos esté cargada
      cargarListaTecnicos();
      
      // Mostrar/ocultar elementos relevantes
      document.getElementById('tecnicoTransf').style.display = 'block';
      document.getElementById('motivo_tran_contain').style.display = 'block';
      document.getElementById('motivo_tran').value = 'Por transferencia a otro tecnico';
      document.getElementById('motivoASuper').style.display = 'none';
      document.getElementById('serie_tran_contain').style.display = 'none';
      document.getElementById('divArchivo').style.display = 'none';
      
      console.log('🔑 Formulario configurado para transferencia a otro técnico');
      
      // Añadir instrucciones para el usuario
      alert('Seleccione el técnico de destino para la transferencia');
    }
    
    // Manejar clic en botón "Enviar requerimiento"
    function handleTransferButtonClick(e) {
      e.preventDefault();
      console.log('👆 Clic en botón transferButton');
      
      // Validar el formulario
      const isTecnicoVisible = document.getElementById('tecnicoTransf').style.display === 'block';
      const usuarioDestinoInput = document.getElementById('usuario_destino');
      const motivo = document.getElementById('motivo_tran').value;
      const serie = document.getElementById('serie_tran').value;
      
      // Validar campos según lo que esté visible
      if (isTecnicoVisible && (!usuarioDestinoInput || usuarioDestinoInput.value === '')) {
        alert('Debe seleccionar un técnico de destino');
        return;
      }
      
      if (!motivo || motivo.trim().length < 5) {
        alert('Debe ingresar un motivo válido (mínimo 5 caracteres)');
        return;
      }
      
      // Obtener valores para la transferencia
      let tecnicoDestino = '';
      if (isTecnicoVisible && usuarioDestinoInput) {
        tecnicoDestino = usuarioDestinoInput.value;
      } else {
        // Si no es visible, usar el valor del campo oculto o val_tecnico_destino
        tecnicoDestino = document.getElementById('usuario_destino_hidden').value || 
                        document.getElementById('val_tecnico_destino').value || '164';
      }
      
      // Verificar si tenemos un motivo por transferencia a otro técnico
      const esPorTransferencia = motivo.includes('transferencia a otro tecnico');
      
      console.log('🔑 Ejecutando transferencia:', {
        serie: serie,
        tecnicoDestino: tecnicoDestino,
        motivo: motivo,
        esPorTransferencia: esPorTransferencia
      });
      
      // Para transferencias a otros técnicos el motivo debe ser exactamente "Por transferencia a otro tecnico"
      if (isTecnicoVisible) {
        // Ejecutar transferencia a otro técnico - esto activará el caso MOV_TRANSFIERE_TECNICO en el servidor
        const idTecnicoOrigen = window.ModLogisticaConfig && window.ModLogisticaConfig.user ? window.ModLogisticaConfig.user.id : '0';
        
        // Mostrar datos de prueba para depurar
        console.log('🔑 Datos para transferencia a otro técnico:', {
          serie: serie,
          origen: idTecnicoOrigen,
          destino: tecnicoDestino
        });
        
        // El orden correcto de los parámetros es: Serial, Ticket, accion, id_tecnico_destino, motivo
        // Al transferir a otro técnico, actualizar inmediatamente la interfaz
        const currentRow = document.querySelector(`tr[data-serial="${serie}"]`);
        if (currentRow) {
          // Animar la fila antes de eliminarla
          currentRow.style.transition = 'all 0.5s ease';
          currentRow.style.backgroundColor = '#ffecb3';
          currentRow.style.opacity = '0.5';
          
          // Eliminar después de la animación
          setTimeout(() => {
            currentRow.remove();
          }, 500);
        }
        
        transferirRegistro(serie, 'Si', 'transfiere', tecnicoDestino, 'Por transferencia a otro tecnico');
      } else {
        // Transferencias normales a bodega/supervisor
        // Asegurar que estamos usando el valor correcto de la serie actual
        console.log('🔑 Transferencia a bodega/supervisor:', {
          serie: serie,
          destino: tecnicoDestino,
          motivo: motivo
        });
        transferirRegistro(serie, serie, 'transfiere', tecnicoDestino, motivo);
      }
    }
    
    // Función para cargar la lista de técnicos directamente desde la base de datos
    function cargarListaTecnicos() {
      console.log('🔍 Cargando lista de técnicos');
      const usuarioDestinoSelect = document.getElementById('usuario_destino');
      
      if (!usuarioDestinoSelect) {
        console.error('❌ No se encontró el elemento select para técnicos');
        return;
      }
      
      // Reiniciar el select
      usuarioDestinoSelect.innerHTML = '<option value="">Seleccione el técnico a transferir</option>';
      
      // Cargar datos directamente desde la consulta SQL insertada como HTML
      const tecnicos = [
        <?php
          // Consulta directa a la base de datos para obtener los técnicos residenciales y QA
          $sql_tecnicos = "SELECT id, nombre, PERFIL FROM tb_user_tqw 
                          WHERE vigente = 'Si' 
                          AND PERFIL IN ('TECNICO RESIDENCIAL', 'user_QA', 'SUPERVISOR') 
                          ORDER BY PERFIL, nombre ASC";
          $result_tecnicos = mysqli_query($conex, $sql_tecnicos);
          
          if ($result_tecnicos && mysqli_num_rows($result_tecnicos) > 0) {
              while ($tecnico = mysqli_fetch_assoc($result_tecnicos)) {
                  // Escapar comillas para evitar problemas con JavaScript
                  $nombre_escapado = str_replace("'", "\'", $tecnico['nombre']);
                  // Agregar etiqueta de perfil para los usuarios QA
                  $etiqueta = ($tecnico['PERFIL'] == 'user_QA') ? '[QA] ' : (($tecnico['PERFIL'] == 'SUPERVISOR') ? '[SUP] ' : '');
                  echo "{ id: '{$tecnico['id']}', nombre: '{$etiqueta}{$nombre_escapado}', perfil: '{$tecnico['PERFIL']}' },";
              }
          }
        ?>
      ];
      
      console.log('💾 Técnicos cargados directamente:', tecnicos.length);
      
      if (tecnicos.length === 0) {
        console.warn('⚠️ No se encontraron técnicos en la base de datos');
        // Agregar datos de prueba en caso de no encontrar técnicos
        tecnicos.push(
          { id: '1339', nombre: 'Alejandro Antonio Mella Torres' },
          { id: '1342', nombre: 'Alejandro Javier Olmedo Fuentes' },
          { id: '1354', nombre: 'Alex Abraham Pinto Urbina' },
          { id: '287', nombre: 'Alexis Eduardo Polanco Galviz' },
          { id: '248', nombre: 'Alfredo Ramon Williamson Roa' }
        );
      }
      
      // Agregar opciones al select
      tecnicos.forEach(tecnico => {
        if (tecnico && tecnico.id && tecnico.nombre) {
          const option = document.createElement('option');
          option.value = tecnico.id;
          option.textContent = tecnico.nombre;
          usuarioDestinoSelect.appendChild(option);
        }
      });
      
      // Estilizar el select
      estilizarSelect();
      
      console.log('✅ Lista de técnicos cargada exitosamente');
    }
    
    // Función para estilizar el select con los colores de la interfaz
    function estilizarSelect() {
      const select = document.getElementById('usuario_destino');
      if (!select) return;
      
      // Verificar si ya tiene la clase para evitar duplicaciones
      if (select.classList.contains('styled-select')) return;
      
      // Marcar como estilizado
      select.classList.add('styled-select');
      
      // Asegurarse de que tenga las clases correctas
      select.classList.add('form-select', 'form-select-sm', 'form-control-clicked');
      
      // Agregar estilos CSS inline para asegurar que se apliquen (solo una vez)
      if (!document.getElementById('tecnico-select-styles')) {
        const style = document.createElement('style');
        style.id = 'tecnico-select-styles';
        style.textContent = `
          /* Estilos para el select de técnicos */
          .form-select.form-select-sm.form-control-clicked {
            appearance: revert;
            background-color: #ffffff;
            border: 1px solid #d63384;
            border-radius: 0.375rem;
            padding: 0.375rem 1.75rem 0.375rem 0.75rem;
            font-size: 0.875rem;
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23d63384' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
          }
          
          .form-select.form-select-sm.form-control-clicked:focus {
            border-color: #d63384;
            box-shadow: 0 0 0 0.25rem rgba(214, 51, 132, 0.25);
            outline: 0;
          }
          
          /* Estilo para las opciones del select */
          #usuario_destino option {
            padding: 8px 12px;
            font-size: 0.875rem;
          }
          
          /* Estilo para las opciones con diferentes perfiles */
          #usuario_destino option[value]:not([value=""]) {
            font-weight: normal;
          }
          
          /* QA tiene un color ligeramente distinto */
          #usuario_destino option[title^="[QA]"] {
            background-color: #f8f2ff;
            color: #9c27b0;
            font-weight: 500;
          }
          
          /* Supervisores con otro color */
          #usuario_destino option[title^="[SUP]"] {
            background-color: #e3f2fd;
            color: #1976d2;
            font-weight: 500;
          }
        `;
        
        document.head.appendChild(style);
      }
      
      // Añadir el atributo title para mostrar tooltip con el nombre completo
      const options = select.querySelectorAll('option');
      options.forEach(option => {
        if (option.value !== '') {
          option.title = option.textContent;
        }
      });
      
      // Organizar las opciones por grupos si no se ha hecho ya
      if (!select.querySelector('optgroup')) {
        organizarOpcionesPorGrupos(select);
      }
    }
    
    // Función para organizar las opciones por grupos según su perfil
    function organizarOpcionesPorGrupos(select) {
      // Obtener todas las opciones existentes
      const options = Array.from(select.querySelectorAll('option'));
      const defaultOption = options.shift(); // Eliminar la opción por defecto
      
      // Crear grupos para cada tipo de perfil
      const tecnicosGroup = document.createElement('optgroup');
      tecnicosGroup.label = 'Técnicos';
      
      const qaGroup = document.createElement('optgroup');
      qaGroup.label = 'QA';
      
      const supervisoresGroup = document.createElement('optgroup');
      supervisoresGroup.label = 'Supervisores';
      
      // Vaciar el select pero mantener la opción por defecto
      select.innerHTML = '';
      select.appendChild(defaultOption);
      
      // Agregar grupos
      select.appendChild(tecnicosGroup);
      select.appendChild(qaGroup);
      select.appendChild(supervisoresGroup);
      
      // Agregar opciones a sus respectivos grupos
      options.forEach(option => {
        const clone = option.cloneNode(true);
        const text = clone.textContent;
        
        if (text.startsWith('[QA]')) {
          clone.textContent = text.replace('[QA] ', '');
          qaGroup.appendChild(clone);
        } else if (text.startsWith('[SUP]')) {
          clone.textContent = text.replace('[SUP] ', '');
          supervisoresGroup.appendChild(clone);
        } else {
          tecnicosGroup.appendChild(clone);
        }
      });
    }
    
    // Manejadores de eventos para botones del offcanvas
    function handleOffcanvasJustificarClick(e) {
      e.preventDefault();
      console.log('👆 Clic en justificarLink');
      
      // Mostrar campos relevantes para justificación con supervisor
      document.getElementById('motivo_tran_contain').style.display = 'none';
      document.getElementById('motivoASuper').style.display = 'block';
      document.getElementById('serie_tran_contain').style.display = 'none';
      document.getElementById('tecnicoTransf').style.display = 'none';
      
      // Asignar valor del supervisor como destino (usar un valor sensible desde PHP)
      const valorSupervisor = 164; // Esto debería obtenerse dinámicamente del supervisor del usuario
      document.getElementById('val_tecnico_destino').value = valorSupervisor;
      
      console.log('🔑 Supervisor asignado:', valorSupervisor);
    }
    
    function handleOffcanvasBodegaSistemicoClick(e) {
      e.preventDefault();
      console.log('👆 Clic en bodegaSistemico');
      
      // Mantener el serial del elemento actual
      const serial = document.getElementById('serie_tran').value;
      console.log('🔍 Serie actual para problema sistémico:', serial);
      
      // Mostrar campos relevantes para problema sistémico
      document.getElementById('motivo_tran_contain').style.display = 'block';
      document.getElementById('motivo_tran').value = 'PROBLEMA SISTEMICO';
      document.getElementById('motivoASuper').style.display = 'none';
      document.getElementById('serie_tran_contain').style.display = 'none';
      document.getElementById('tecnicoTransf').style.display = 'none';
      
      // Asignar supervisor como destino
      const valorSupervisor = 164; // Esto debería obtenerse dinámicamente
      document.getElementById('val_tecnico_destino').value = valorSupervisor;
      
      console.log('🔑 Asignado a supervisor por problema sistémico. Serie:', serial);
    }
    
    function handleOffcanvasBodegaSeriIncorrectaClick(e) {
      e.preventDefault();
      console.log('👆 Clic en bodegaSeriIncorrecta');
      
      // Mantener el serial del elemento actual
      const serial = document.getElementById('serie_tran').value;
      console.log('🔍 Serie actual para serie incorrecta:', serial);
      
      // Mostrar campos relevantes para serie incorrecta
      document.getElementById('motivo_tran_contain').style.display = 'block';
      document.getElementById('motivo_tran').value = 'La serie del equipo es incorrecta.';
      document.getElementById('motivoASuper').style.display = 'none';
      document.getElementById('serie_tran_contain').style.display = 'none';
      document.getElementById('tecnicoTransf').style.display = 'none';
      
      // Asignar supervisor como destino
      const valorSupervisor = 164; // Esto debería obtenerse dinámicamente
      document.getElementById('val_tecnico_destino').value = valorSupervisor;
      
      console.log('🔑 Asignado a supervisor por serie incorrecta. Serie:', serial);
    }
    
    function handleOffcanvasBodegaLinkClick(e) {
      e.preventDefault();
      console.log('👆 Clic en bodegaLink');
      
      // Mantener el serial del elemento actual
      const serial = document.getElementById('serie_tran').value;
      console.log('🔍 Serie actual para fallo electrónico:', serial);
      
      // Mostrar campos relevantes para equipo con desperfecto
      document.getElementById('motivo_tran_contain').style.display = 'block';
      document.getElementById('motivo_tran').value = 'FALLO ELECTRONICO';
      document.getElementById('motivoASuper').style.display = 'none';
      document.getElementById('serie_tran_contain').style.display = 'none';
      document.getElementById('tecnicoTransf').style.display = 'none';
      
      // Asignar bodega como destino
      document.getElementById('usuario_destino_hidden').value = '164';
      
      console.log('🔑 Asignado a bodega por desperfecto. Serie:', serial);
    }
    
    // Manejador para el botón "Serie no aparece en TOA"
    function handleOffcanvasBodegaTOAClick(e) {
      e.preventDefault();
      console.log('👆 Clic en bodegaLinkTOA');
      
      // Mantener el serial del elemento actual
      const serial = document.getElementById('serie_tran').value;
      console.log('🔍 Serie actual para problema TOA:', serial);
      
      // Mostrar campos relevantes para serie no aparece en TOA
      document.getElementById('motivo_tran_contain').style.display = 'block';
      document.getElementById('motivo_tran').value = 'SERIE NO APARECE EN TOA';
      document.getElementById('motivoASuper').style.display = 'none';
      document.getElementById('serie_tran_contain').style.display = 'none';
      document.getElementById('tecnicoTransf').style.display = 'none';
      
      // Asignar bodega como destino
      document.getElementById('usuario_destino_hidden').value = '164';
      
      console.log('🔑 Asignado a bodega por problema TOA. Serie:', serial);
    }
    
    // Manejador para el botón "Serie a regularizar por cierre de inventario"
    function handleOffcanvasFotoCierreInvClick(e) {
      e.preventDefault();
      console.log('👆 Clic en fotoCierreInv');
      
      // Mantener el serial del elemento actual
      const serial = document.getElementById('serie_tran').value;
      console.log('🔍 Serie actual para cierre inventario:', serial);
      
      // Mostrar campos relevantes para regularización por cierre de inventario
      document.getElementById('motivo_tran_contain').style.display = 'block';
      document.getElementById('motivo_tran').value = 'REGULARIZACION POR CIERRE DE INVENTARIO';
      document.getElementById('motivoASuper').style.display = 'none';
      document.getElementById('serie_tran_contain').style.display = 'none';
      document.getElementById('tecnicoTransf').style.display = 'none';
      
      // Asignar bodega como destino
      document.getElementById('usuario_destino_hidden').value = '164';
      
      console.log('🔑 Asignado a bodega por cierre inventario. Serie:', serial);
    }
    
    // Manejador para el botón "Devuelto a bodega"
    function handleOffcanvasDevueltoBodegaClick(e) {
      e.preventDefault();
      console.log('👆 Clic en DevueltoBodega');
      
      // Mantener el serial del elemento actual
      const serial = document.getElementById('serie_tran').value;
      console.log('🔍 Serie actual para devolución a bodega:', serial);
      
      // Mostrar campos relevantes para devolución a bodega
      document.getElementById('motivo_tran_contain').style.display = 'block';
      document.getElementById('motivo_tran').value = 'DEVUELTO A BODEGA';
      document.getElementById('motivoASuper').style.display = 'none';
      document.getElementById('serie_tran_contain').style.display = 'none';
      document.getElementById('tecnicoTransf').style.display = 'none';
      
      // Asignar bodega como destino
      document.getElementById('usuario_destino_hidden').value = '164';
      
      console.log('🔑 Asignado a bodega por devolución. Serie:', serial);
    }
    
    // Inicialización del sistema de lazy loading
    document.addEventListener('DOMContentLoaded', function() {
      // Garantizar que el título se mantenga correcto incluso después de la carga
      document.title = "Módulo Logística";
      // Inicializar sistemas básicos
      initTabSystem();
      initSearchSystem();
      setupActionButtons();
      
      // Cargar datos de recepción automáticamente al inicio
      loadTableData('recepcion');
      
      // Configurar botones del offcanvas
      setupOffcanvasButtons();

      // Asegurar que la tabla recepción esté visible inmediatamente
      const recepcionSection = document.querySelector('[data-table="recepcion"]');
      if (recepcionSection) {
        recepcionSection.style.setProperty('display', 'block', 'important');
      }

      // La carga inicial ya se realiza directamente arriba con loadTableData('recepcion')

      // Exponer funciones globalmente para debugging
      window.LazyLoadingLogistica = {
        loadTable: loadTableData,
        clearCache: function(tableName) {
          if (tableName) {
            tableCache[tableName] = null;
            const tabButton = document.querySelector(`[data-tab="${tableName}"]`);
            if (tabButton) {
              tabButton.classList.remove('loaded');
            }
          } else {
            Object.keys(tableCache).forEach(key => {
              tableCache[key] = null;
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
              btn.classList.remove('loaded');
            });
          }
        },
        getCache: () => tableCache,
        getLoadingState: () => loadingState
      };

    });

    // Función para eliminar fila con animación
    function removeRowWithAnimation(row, callback) {
      if (!row) return;
      
      // Agregar clase de animación
      row.classList.add('row-fadeout');
      
      // Esperar a que termine la animación antes de eliminar
      setTimeout(() => {
        if (row && row.parentNode) {
          row.remove();
          if (typeof callback === 'function') {
            callback();
          }
        }
      }, 500); // Este tiempo debe coincidir con la duración de la animación
    }
    
    // ========== FUNCIÓN PARA BOTÓN ACEPTAR DE RECEPCIÓN ==========
    function actualizarRegistro(Serial, ticket, id_tecnico, accion) {
      
      // Obtener el elemento del botón que disparó el evento
      const buttonElement = event.target.closest('button');
      
      // Crear FormData para enviar los datos en POST
      var formData = new FormData();
      
      if (accion === 'ENTREGA_REV') {
        if (id_tecnico === '6') {
          mostrarNotificacion('Bodega tiene pendiente la confirmación', 'warning');
        } else if (id_tecnico === '7') {
          mostrarNotificacion('Material pendiente de revisión por supervisor', 'warning');
        } else {
          // Lógica para entrega reversa
          formData.append('Serial', Serial);
          formData.append('accion', accion);
          formData.append('id_tecnico_origen', userId);
          formData.append('ticket', ticket);
          
          enviarSolicitudAceptacion(formData, buttonElement);
        }
      } else {
        if (accion === 'ACEPTA') {
          // ID_MOVIMIENTO - Validación específica
          if (id_tecnico == 2) {
            mostrarNotificacion("NO PODRÁS CONFIRMAR HASTA LA REVISIÓN DE BODEGA", 'warning');
          } else {
            // Preparar datos para aceptación
            formData.append('Serial', Serial);
            formData.append('accion', accion);
            formData.append('id_tecnico_origen', userId);
            formData.append('ticket', ticket);
            
            enviarSolicitudAceptacion(formData, buttonElement);
          }
        }
      }
    }

    // Función auxiliar para enviar la solicitud de aceptación
    function enviarSolicitudAceptacion(formData, buttonElement) {
      // Obtener el serial del material antes de procesar
      const row = buttonElement.closest('tr');
      const serialCell = row ? row.querySelector('td:first-child') : null;
      const serialNumber = serialCell ? serialCell.textContent.trim() : null;
      
      // Realiza la solicitud POST utilizando AJAX
      var request = new XMLHttpRequest();
      request.open('POST', 'GET_LOGIS_DIRECTA.php');
      
      request.onload = function() {
        // Procesa la respuesta del servidor
        if (request.status === 200) {
          // Mostrar mensaje de éxito usando la función de notificación en lugar de alert
          mostrarNotificacion('Material aceptado correctamente', 'success');
          
          // ACTUALIZAR CACHE de manera inteligente en lugar de limpiarlo completamente
          updateCacheAfterAcceptance(serialNumber);
          console.log('🔄 Cache actualizado inteligentemente para serial:', serialNumber);
          
          // ELIMINAR LA FILA INMEDIATAMENTE de recepción con animación
          // No esperamos al SSE porque el material ya no califica para recepción
          if (row && serialNumber) {
            console.log('🗑️ Eliminando fila de recepción inmediatamente:', serialNumber);
            
            // Usar la función centralizada para remover con animación
            removeRowWithAnimation(row, () => {
              // Mostrar mensaje informativo
              const recepcionTableBody = document.getElementById('recepcionTableBody');
              if (recepcionTableBody && recepcionTableBody.children.length === 0) {
                recepcionTableBody.innerHTML = `
                  <tr>
                    <td colspan="3" class="text-center text-muted">
                      <i class="bi bi-inbox me-2"></i>
                      No hay materiales pendientes de recepción
                    </td>
                  </tr>
                `;
              }
            });
          }
          
          // El SSE agregará el material a la tabla directa automáticamente
          
        } else {
          console.error('❌ Error en solicitud:', request.status, request.statusText);
          mostrarNotificacion('Error al procesar la solicitud. Código: ' + request.status, 'danger');
        }
      };
      
      request.onerror = function() {
        console.error('❌ Error de red en solicitud');
        mostrarNotificacion('Error de conexión. Verifique su red e intente nuevamente.', 'danger');
      };
      
      request.send(formData);
    }
    
    // Función auxiliar para limpiar cache de tabla específica
    function clearTableCache(tableName) {
      if (tableCache[tableName]) {
        tableCache[tableName] = null;
        loadingState[tableName] = false;
        
        const tabButton = document.querySelector(`[data-tab="${tableName}"]`);
        if (tabButton) {
          tabButton.classList.remove('loaded');
        }
      }
    }

    // Función inteligente para actualizar cache después de aceptar material
    function updateCacheAfterAcceptance(serialNumber) {
      // 1. Remover el registro del cache de recepción (si existe)
      if (tableCache['recepcion'] && Array.isArray(tableCache['recepcion'])) {
        tableCache['recepcion'] = tableCache['recepcion'].filter(item => {
          const itemSerial = item.Serial || item.serie || '';
          return itemSerial !== serialNumber;
        });
        console.log('🗑️ Registro removido del cache de recepción:', serialNumber);
      }

      // 2. El SSE se encargará de agregar a directa, pero podemos preparar el cache
      // No limpiamos el cache de directa, permitimos que el SSE lo actualice naturalmente
      
      // 3. Si las tablas están vacías después de la actualización, mostrar mensaje
      setTimeout(() => {
        const recepcionTableBody = document.getElementById('recepcionTableBody');
        if (recepcionTableBody && recepcionTableBody.children.length === 0) {
          if (tableCache['recepcion'] && tableCache['recepcion'].length === 0) {
            recepcionTableBody.innerHTML = `
              <tr>
                <td colspan="3" class="text-center text-muted">
                  <i class="bi bi-inbox me-2"></i>
                  No hay materiales pendientes de recepción
                </td>
              </tr>
            `;
          }
        }
      }, 100);
    }

    // ========== IMPLEMENTACIÓN SSE PARA ACTUALIZACIONES EN TIEMPO REAL ==========
    let eventSource;

    function initSSE() {
      var userIdSSE = userId; // Usar la variable userId ya definida
      eventSource = new EventSource('sse_logistica.php?user_id=' + userIdSSE);
      
      eventSource.onmessage = function(event) {
        const data = JSON.parse(event.data);
        
        if (data.type === 'log') {
          // Logs silenciosos para SSE
          console.log('📝 SSE Log:', data.message);
        } else if (data.type === 'update') {
          console.log('🔄 SSE Update recibido:', data);
          handleSSETableUpdate(data);
        } else {
          console.log('❓ SSE Mensaje desconocido:', data);
        }
      };
      
      eventSource.onerror = function(error) {
        console.error('SSE Error:', error);
        eventSource.close();
        // Auto-reconectar después de 5 segundos
        setTimeout(initSSE, 5000);
      };
    }

    function handleSSETableUpdate(data) {
      console.log('🔄 SSE Update recibido:', data);
      
      // Mapear los nombres de tabla del sistema original a los nuevos
      let targetTable = null;
      
      switch(data.table) {
        case 'TablaDirecta':
          targetTable = 'directaTableBody';
          break;
        case 'tablaAsignacion':
          targetTable = 'recepcionTableBody';
          break;
        case 'poolBodegaReversa':
          targetTable = 'reversaTableBody';
          break;
        default:
          console.log('❌ Tabla no reconocida:', data.table);
          return; // Tabla no reconocida
      }
      
      const tableBody = document.getElementById(targetTable);
      if (!tableBody) {
        console.log('❌ No se encontró el tbody:', targetTable);
        return;
      }
      
      console.log('✅ Procesando update para tabla:', data.table, '-> tbody:', targetTable);
      
      // CASO ESPECIAL: Rechazo de Bodega (ID_MOVIMIENTO = 11) debe actualizar ambas tablas
      if (data.id_movimiento === '11' && data.semantica === 'Rechaza Bodega') {
        console.log('🚨 Detectado rechazo de bodega para serial:', data.serial);
        // Actualizar ambas tablas (directa y reversa)
        handleCrossTableUpdate(data);
        return;
      }
      
      // Lógica específica por tabla (replicando el comportamiento original)
      if (data.table === 'TablaDirecta') {
        handleDirectaSSEUpdate(tableBody, data);
      } else if (data.table === 'tablaAsignacion') {
        handleRecepcionSSEUpdate(tableBody, data);
      } else if (data.table === 'poolBodegaReversa') {
        handleReversaSSEUpdate(tableBody, data);
      }
    }
    
    // Función para manejar actualizaciones que afectan a múltiples tablas
    function handleCrossTableUpdate(data) {
      console.log('📌 Actualizando múltiples tablas para serial:', data.serial);
      
      // Determinar el tipo de actualización cruzada
      const esRechazoBodega = data.id_movimiento == 11 && data.semantica === 'Rechaza Bodega';
      const esEntregaDeclarada = data.id_movimiento == 12;
      
      // Mensaje de notificación por defecto
      let mensajeNotificacion = '';
      let tipoNotificacion = 'info';
      
      // 1. Manejo de Rechazo de Bodega
      if (esRechazoBodega) {
        console.log('🔍 [DEBUG] Procesando rechazo de bodega para serie:', data.serial);
        
        // Actualizar el estado en la tabla de reversa
        const reversaTableBody = document.getElementById('reversaTableBody');
        if (reversaTableBody) {
          // Buscar si ya existe una fila con este serial
          const existingRows = reversaTableBody.querySelectorAll(`tr[data-serial="${data.serial}"]`);
          if (existingRows.length > 0) {
            // Si existe, actualizarla con el nuevo estado
            console.log('🔄 Actualizando fila existente en reversa para:', data.serial);
            existingRows.forEach(row => {
              // Actualizar el estado en la celda existente
              const statusCell = row.querySelector('td:nth-child(2)');
              if (statusCell) {
                // Actualizar con el nuevo estado y estilo
                statusCell.innerHTML = '<span class="status-badge rechaza-bodega">Rechaza Bodega</span>';
                console.log('✅ Estado actualizado para fila en reversa:', data.serial);
              } else {
                // Si no podemos actualizar, eliminamos y creamos nueva
                removeRowWithAnimation(row);
                // Se creará una nueva fila abajo
              }
            });
          } else {
            console.log('⚠️ No se encontró fila existente en reversa para:', data.serial);
            // Si no existe, creamos una nueva
          }
          
          // Si no había fila o se eliminó, agregar una nueva
          if (existingRows.length === 0 || existingRows[0].querySelector('td:nth-child(2)') === null) {
            // Crear un nuevo HTML para la fila con el formato correcto para REVERSA
            const newRowHtml = createFormattedRowHtml('reversa', data.serial, 'Rechaza Bodega', data.id_movimiento || '11');
            
            // Insertar la nueva fila en la tabla reversa
            reversaTableBody.insertAdjacentHTML('beforeend', newRowHtml);
            console.log('✅ Nueva fila agregada para:', data.serial, 'en tabla REVERSA');
          }
          
          // Actualizar cache de reversa
          if (!tableCache['reversa']) {
            tableCache['reversa'] = [];
          }
          
          // Eliminar registro existente si lo hay
          const existingIndex = tableCache['reversa'].findIndex(item => {
            const itemSerial = item.Serial || item.serie || '';
            return itemSerial === data.serial;
          });
          
          if (existingIndex !== -1) {
            // Actualizar registro existente
            tableCache['reversa'][existingIndex] = {
              Serial: data.serial,
              serie: data.serial,
              Semantica: 'Rechaza Bodega',
              id_movimiento: '11'
            };
            console.log('🔄 Registro actualizado en cache de reversa:', data.serial);
          } else {
            // Agregar nuevo registro
            tableCache['reversa'].push({
              Serial: data.serial,
              serie: data.serial,
              Semantica: 'Rechaza Bodega',
              id_movimiento: '11'
            });
            console.log('➕ Nuevo registro agregado al cache de reversa:', data.serial);
          }
        }
        
        // Mensaje para notificación
        mensajeNotificacion = `La serie ${data.serial} ha sido rechazada por bodega`;
        tipoNotificacion = 'warning';
      }
      // 2. Manejo de Entrega Declarada
      else if (esEntregaDeclarada) {
        // 2.1. Actualizar tabla de reversa (ELIMINAR FILA, NO ACTUALIZAR)
        const reversaTableBody = document.getElementById('reversaTableBody');
        if (reversaTableBody) {
          // Buscar y eliminar TODAS las filas con este serial para asegurar limpieza completa
          console.log('🧹 ELIMINANDO COMPLETAMENTE fila de reversa por entrega declarada:', data.serial);
          
          // Obtener todas las filas que coincidan con el serial
          const matchingRows = reversaTableBody.querySelectorAll(`tr[data-serial="${data.serial}"]`);
          if (matchingRows.length > 0) {
            console.log(`🗑️ Encontradas ${matchingRows.length} filas para eliminar con serial:`, data.serial);
            // Eliminar cada fila con animación
            matchingRows.forEach(row => {
              console.log('💥 Eliminando fila en reversa para:', data.serial);
              removeRowWithAnimation(row);
            });
          } else {
            // Búsqueda manual como respaldo
            console.log('⚠️ No se encontraron filas por atributo, buscando por contenido de celda...');
            const rows = reversaTableBody.getElementsByTagName('tr');
            for (let i = 0; i < rows.length; i++) {
              const cells = rows[i].getElementsByTagName('td');
              if (cells.length > 0 && cells[0].innerText === data.serial) {
                console.log('💥 Eliminando fila en reversa para:', data.serial);
                removeRowWithAnimation(rows[i]);
                // NO hacemos break - eliminamos TODAS las coincidencias
              }
            }
          }
          
          // Asegurarse de que se elimina del cache
          if (tableCache['reversa'] && Array.isArray(tableCache['reversa'])) {
            const initialLength = tableCache['reversa'].length;
            tableCache['reversa'] = tableCache['reversa'].filter(item => {
              const itemSerial = item.Serial || item.serie || '';
              return itemSerial !== data.serial;
            });
            const removedCount = initialLength - tableCache['reversa'].length;
            console.log(`🧹 ${removedCount} registros eliminados del cache de reversa para:`, data.serial);
          }
        }
        
        // Mensaje para notificación
        mensajeNotificacion = `La serie ${data.serial} ha sido declarada como entregada correctamente`;
        tipoNotificacion = 'success';
      }
      
      // 3. Aplicar cambios visuales y reinicializar botones
      setupActionButtons();
      setupReversaButtons();
      
      // 4. Mostrar notificación al usuario
      if (mensajeNotificacion) {
        mostrarNotificacion(mensajeNotificacion, tipoNotificacion);
      }
    }
    
    // Función para crear HTML de fila con formato consistente - VERSION ESTANDARIZADA
    function createFormattedRowHtml(tableName, serial, estado, idMovimiento, params = {}) {
      // Determinar la clase CSS para el estado
      let clase_estado = 'default';
      
      // Asignar clase CSS basada en el estado
      const estadoLower = estado.toLowerCase();
        
      // Caso especial para Rechaza bodega - naranja oscuro
      if (estadoLower === 'rechaza bodega' || estadoLower === 'rechaza bodega justificación') {
        clase_estado = 'rechaza-bodega';
      }
      // Verificar si es un estado de rechazo (contiene la palabra 'rechaza')
      else if (estadoLower.includes('rechaza') || estadoLower.includes('rechazo') || 
               estadoLower === 'faltante') {
        clase_estado = 'rechazo';
      }
      // Estado disponible - verde con texto blanco
      else if (estadoLower === 'disponible') {
        clase_estado = 'disponible';
      }
      // Todos los demás estados - amarillo con texto negro
      else {
        clase_estado = 'default';
      }
      
      // Generar los botones apropiados basados en el tipo de tabla
      let actionButtonsHtml = '';
      
      if (tableName === 'directa') {
        // Botones estándar para tabla directa
        actionButtonsHtml = `
          <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serial}">
            <i class="bi bi-clock-history"></i>
          </button>
          <button class="action-btn declarar-btn" title="Declarar" data-serie="${serial}" data-id-movimiento="${idMovimiento}">
            <i class="bi bi-clipboard-check"></i>
          </button>
          <button class="action-btn justificar-btn transfiere-btn" title="Transferir" data-serie="${serial}" 
                  onclick="redirigirEnTransferencia('${serial}', '${serial}', '${idMovimiento}', 'TRANSFIERE')">
            <i class="bi bi-send-fill"></i>
          </button>
        `;
      } else if (tableName === 'recepcion') {
        // Botones para tabla recepcion
        const idTecnicoDestino = params.idTecnicoDestino || params.id_transfer || '';
        const variableCondicion = (idMovimiento == 15) ? "Si" : idTecnicoDestino;
        const idTecnicoOrigen = params.idTecnicoOrigen || params.ticket || '';
        
        actionButtonsHtml = `
          <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serial}">
            <i class="bi bi-clock-history"></i>
          </button>
          <button class="action-btn aceptar-btn btn btn-success" title="Aceptar Material" 
                  onclick="actualizarRegistro('${serial}', '${variableCondicion}', '${idMovimiento}', 'ACEPTA')">
            <i class="bi bi-check-circle"></i>
          </button>
          <button class="action-btn justificar-btn" title="Rechazar Material" 
                  onclick="rechazoMaterial('${serial}', '${idTecnicoOrigen}', '${idTecnicoDestino}', 'RECHAZA')">
            <i class="bi bi-journal-x"></i>
          </button>
        `;
      } else if (tableName === 'reversa') {
        // Botones específicos para la tabla de reversa
        actionButtonsHtml = `
          <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serial}">
            <i class="bi bi-clock-history"></i>
          </button>
          <button class="action-btn declarar-rev-btn" title="Declarar Entrega" data-serie="${serial}" data-id-movimiento="${idMovimiento || '0'}">
            <i class="bi bi-box-seam"></i>
          </button>
          <button class="action-btn transferir-rev-btn" title="A Supervisor" data-serie="${serial}" data-id-movimiento="${idMovimiento || '0'}">
            <i class="bi bi-send"></i>
          </button>
        `;
      } else {
        // Botones estándar para cualquier otra tabla (faltante, etc.)
        actionButtonsHtml = `
          <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serial}">
            <i class="bi bi-clock-history"></i>
          </button>
          <button class="action-btn declarar-btn" title="Declarar" data-serie="${serial}" data-id-movimiento="${idMovimiento}">
            <i class="bi bi-clipboard-check"></i>
          </button>
          <button class="action-btn justificar-btn" title="Justificar" data-serie="${serial}">
            <i class="bi bi-shield-check"></i>
          </button>
        `;
      }
      
      // Crear la fila HTML completa
      return `
        <tr data-type="${tableName}" data-serial="${serial}">
          <td>${serial}</td>
          <td><span class="status-badge ${clase_estado}">${estado}</span></td>
          <td>
            <div class="action-buttons-container">
              ${actionButtonsHtml}
            </div>
          </td>
        </tr>
      `;
    }
    
    // Función para mostrar notificaciones al usuario
    function mostrarNotificacion(mensaje, tipo = 'info') {
      // Crear el elemento de notificación
      const notificacion = document.createElement('div');
      notificacion.className = `alert alert-${tipo} alert-dismissible fade show notification-toast`;
      notificacion.innerHTML = `
        ${mensaje}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      `;
      
      // Estilos para la notificación flotante
      notificacion.style.position = 'fixed';
      notificacion.style.top = '10px';
      notificacion.style.right = '10px';
      notificacion.style.zIndex = '9999';
      notificacion.style.minWidth = '300px';
      notificacion.style.maxWidth = '500px';
      notificacion.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
      notificacion.style.opacity = '0';
      notificacion.style.transition = 'opacity 0.3s ease-in-out';
      
      // Agregar al documento
      document.body.appendChild(notificacion);
      
      // Mostrar con animación
      setTimeout(() => {
        notificacion.style.opacity = '1';
      }, 10);
      
      // Auto-eliminar después de 5 segundos
      setTimeout(() => {
        notificacion.style.opacity = '0';
        setTimeout(() => {
          if (notificacion.parentNode) {
            notificacion.parentNode.removeChild(notificacion);
          }
        }, 300);
      }, 5000);
    }

    function handleDirectaSSEUpdate(tableBody, data) {
      console.log('🎯 Procesando directa SSE para serial:', data.serial, 'id_movimiento:', data.id_movimiento);
      
      // VERIFICAR SI LA TABLA DIRECTA HA SIDO CARGADA
      if (!tableCache['directa']) {
        console.log('⚠️ Tabla directa no cargada, cargando automáticamente...');
        // Cargar tabla directa primero, luego procesar el update
        loadTableData('directa').then(() => {
          console.log('✅ Tabla directa cargada, procesando update SSE...');
          // Una vez cargada, procesar el update normalmente
          handleDirectaSSEUpdateAfterLoad(tableBody, data);
        }).catch(error => {
          console.error('❌ Error cargando tabla directa:', error);
          // Si falla la carga, procesar el update de todas formas
          handleDirectaSSEUpdateAfterLoad(tableBody, data);
        });
        return;
      }
      
      // Si ya está cargada, procesar normalmente
      handleDirectaSSEUpdateAfterLoad(tableBody, data);
    }

    function handleDirectaSSEUpdateAfterLoad(tableBody, data) {
      console.log('🎯 Procesando directa SSE (después de carga) para serial:', data.serial, 'id_movimiento:', data.id_movimiento);
      
      // Replicar lógica original: buscar fila por serial y eliminarla
      const rows = tableBody.getElementsByTagName('tr');
      let rowUpdated = false;
      
      // Buscar y eliminar fila existente por serial (primera columna)
      for (let i = 0; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        if (cells.length > 0 && cells[0].innerText === data.serial) {
          console.log('💥 Eliminando fila existente para:', data.serial);
          rowUpdated = true;
          removeRowWithAnimation(rows[i]);
          break;
        }
      }
      
      // Si id_movimiento es 15 (transferencia), no agregar nueva fila
      if (data.id_movimiento == 15) {
        console.log('🚨 No se agrega fila para id_movimiento 15 (transferencia)');
        
        // ACTUALIZAR CACHE DE DIRECTA inteligentemente
        addToDirectaCache(data);
      } else {
        // Para cualquier otro movimiento, agregar nueva fila con formato estandarizado
        const semantica = data.semantica || 'Pendiente';
        
        // Usar la función estandarizada para crear el HTML de la fila
        const newRowHtml = createFormattedRowHtml('directa', data.serial, semantica, data.id_movimiento);
        
        tableBody.insertAdjacentHTML('beforeend', newRowHtml);
        console.log('✅ Nueva fila agregada para:', data.serial, 'en tabla directa con formato estandarizado');
        
        // ACTUALIZAR CACHE DE DIRECTA inteligentemente
        addToDirectaCache(data);
      }
      
      setupActionButtons();
    }

    // Función para agregar registros al cache de directa de manera inteligente
    function addToDirectaCache(data) {
      if (!tableCache['directa']) {
        tableCache['directa'] = [];
      }
      
      // Verificar si el registro ya existe en el cache
      const existingIndex = tableCache['directa'].findIndex(item => {
        const itemSerial = item.Serial || item.serie || '';
        return itemSerial === data.serial;
      });
      
      // Crear objeto de datos para el cache
      const newCacheItem = {
        Serial: data.serial,
        serie: data.serial,
        Semantica: data.semantica || 'Disponible',
        id_movimiento: data.id_movimiento
      };
      
      if (existingIndex !== -1) {
        // Actualizar registro existente
        tableCache['directa'][existingIndex] = newCacheItem;
        console.log('🔄 Registro actualizado en cache de directa:', data.serial);
      } else {
        // Agregar nuevo registro
        tableCache['directa'].push(newCacheItem);
        console.log('➕ Nuevo registro agregado al cache de directa:', data.serial);
      }
    }

    function handleRecepcionSSEUpdate(tableBody, data) {
      console.log('🎯 Procesando recepción SSE para serial:', data.serial, 'id_movimiento:', data.id_movimiento);
      
      // Replicar lógica original: SIEMPRE eliminar fila por serial 
      // IMPORTANTE: En mod_logistica.php el serial está en la PRIMERA columna (índice 0)
      const rows = tableBody.getElementsByTagName('tr');
      console.log('📊 Filas encontradas en tabla recepción:', rows.length);
      
      let filaEliminada = false;
      
      // Buscar y eliminar fila existente por serial (PRIMERA columna - índice 0)
      for (let i = 0; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        if (cells.length > 0) {
          const cellSerial = cells[0].innerText.trim();
          console.log(`🔍 Comparando fila ${i}: '${cellSerial}' vs '${data.serial}'`);
          
          if (cellSerial === data.serial) {
            filaEliminada = true;
            console.log('✅ Fila eliminada de recepcionTableBody:', data.serial);
            removeRowWithAnimation(rows[i]);
            break;
          }
        }
      }
      
      if (!filaEliminada) {
        console.log('⚠️ No se encontró fila para eliminar con serial:', data.serial);
      }
      
      // Solo agregar nueva fila si id_movimiento != 24 Y no es un rechazo (id_movimiento != 2)
      if (data.id_movimiento != 24 && data.id_movimiento != 2) {
        // Extraer la semantica del data o usar un valor por defecto
        const semantica = data.semantica || data.tipo_movimiento || 'Pendiente por recibir';
        
        // Crear parámetros adicionales para createFormattedRowHtml
        const params = {
          idTecnicoDestino: data.id_tecnico_destino || data.id_transfer || '',
          idTecnicoOrigen: data.id_tecnico_origen || ''
        };
        
        // Usar la función estandarizada para crear el HTML de la fila
        const newRowHtml = createFormattedRowHtml('recepcion', data.serial, semantica, data.id_movimiento, params);
        
        tableBody.insertAdjacentHTML('beforeend', newRowHtml);
        console.log('➕ Nueva fila agregada para serial:', data.serial, 'con formato estandarizado');
      } else {
        console.log('🚫 No se agrega nueva fila (id_movimiento = ' + data.id_movimiento + ')');
      }
      
      setupActionButtons();
    }

    function handleReversaSSEUpdate(tableBody, data) {
      console.log('🎯 Procesando reversa SSE para serial:', data.serial, 'id_movimiento:', data.id_movimiento);
      
      // VERIFICAR SI LA TABLA REVERSA HA SIDO CARGADA
      if (!tableCache['reversa']) {
        console.log('⚠️ Tabla reversa no cargada, cargando automáticamente...');
        // Cargar tabla reversa primero, luego procesar el update
        loadTableData('reversa').then(() => {
          console.log('✅ Tabla reversa cargada, procesando update SSE...');
          // Una vez cargada, procesar el update normalmente
          handleReversaSSEUpdateAfterLoad(tableBody, data);
        }).catch(error => {
          console.error('❌ Error cargando tabla reversa:', error);
          // Si falla la carga, procesar el update de todas formas
          handleReversaSSEUpdateAfterLoad(tableBody, data);
        });
        return;
      }
      
      // Si ya está cargada, procesar normalmente
      handleReversaSSEUpdateAfterLoad(tableBody, data);
    }
    
    function handleReversaSSEUpdateAfterLoad(tableBody, data) {
      console.log('🎯 Procesando reversa SSE (después de carga) para serial:', data.serial, 'id_movimiento:', data.id_movimiento);
      
      // Actualizar cache para la tabla reversa
      updateReversaCache(data);
      
      // Replicar lógica original: eliminar fila y siempre agregar nueva
      const rows = tableBody.getElementsByTagName('tr');
      let rowUpdated = false;
      
      // Buscar y eliminar fila existente por serial (primera columna)
      for (let i = 0; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        if (cells.length > 0 && cells[0].innerText === data.serial) {
          console.log('💥 Eliminando fila existente para:', data.serial);
          rowUpdated = true;
          removeRowWithAnimation(rows[i]);
          break;
        }
      }
      
      // Verificar estados que requieren manejo especial
      
      // 1. Para estado de rechazo de bodega (id_movimiento = 11), no agregar a la tabla de reversa
      // porque esto se manejará en handleCrossTableUpdate para asegurar consistencia
      if (data.id_movimiento == 11 && data.semantica === 'Rechaza Bodega') {
        console.log('🚨 Omitiendo agregar fila en reversa para rechazo de bodega - se manejará con actualización dirigida');
        return;
      }
      
      // 2. Para entrega declarada (id_movimiento = 12 o semantica = 'Entregado por Validar'), eliminar COMPLETAMENTE de la tabla de reversa
      if (data.id_movimiento == 12 || data.semantica === 'Entregado por Validar') {
        console.log('🚨 Material declarado como entregado/validar. ELIMINANDO COMPLETAMENTE de reversa:', data.serial);
        // No agregar nueva fila, solo eliminar la existente y asegurarnos de limpiar el cache
        
        // Forzar eliminación del cache
        if (tableCache['reversa'] && Array.isArray(tableCache['reversa'])) {
          const initialLength = tableCache['reversa'].length;
          tableCache['reversa'] = tableCache['reversa'].filter(item => {
            const itemSerial = item.Serial || item.serie || '';
            return itemSerial !== data.serial;
          });
          const removedCount = initialLength - tableCache['reversa'].length;
          console.log(`🧹 [Limpieza forzada] ${removedCount} registros eliminados del cache de reversa para:`, data.serial);
        }
        
        // Eliminar también del DOM si existe
        const reversaTableBody = document.getElementById('reversaTableBody');
        if (reversaTableBody) {
          const rowsToRemove = reversaTableBody.querySelectorAll(`tr[data-serial="${data.serial}"]`);
          if (rowsToRemove.length > 0) {
            console.log(`🗑️ Encontradas ${rowsToRemove.length} filas adicionales para eliminar`);
            rowsToRemove.forEach(row => {
              removeRowWithAnimation(row);
            });
          }
        }
        
        return;
      }
      
      // 3. Para transferencias a supervisor (id_movimiento = 6, 7), actualizar con estado especial
      const esTransferenciaSupervisor = data.id_movimiento == 6 || data.id_movimiento == 7;
      let semantica = data.semantica || 'Reversa';
      
      if (esTransferenciaSupervisor) {
        semantica = 'Pendiente de revisión';
        console.log('🔄 Transferencia a supervisor detectada:', data.serial);
      }
      
      // Crear parámetros adicionales para createFormattedRowHtml si es necesario
      const params = {
        idTecnicoDestino: data.id_tecnico_destino || '',
        idTecnicoOrigen: data.id_tecnico_origen || ''
      };
      
      // Usar la función estandarizada para crear el HTML de la fila con los botones correctos para reversa
      const newRowHtml = createFormattedRowHtml('reversa', data.serial, semantica, data.id_movimiento, params);
      
      tableBody.insertAdjacentHTML('beforeend', newRowHtml);
      console.log('✅ Nueva fila agregada para:', data.serial, 'en tabla reversa con formato estandarizado');
      
      // Asegurar que los botones específicos de reversa estén configurados
      setupActionButtons();
      setupReversaButtons();
      
      // Mostrar notificación según el tipo de movimiento
      if (esTransferenciaSupervisor) {
        mostrarNotificacion(`Material ${data.serial} transferido a supervisor para revisión`, 'info');
      } else if (data.id_movimiento == 12) {
        mostrarNotificacion(`Material ${data.serial} declarado como entregado correctamente`, 'success');
      }
    }
    
    // Función para actualizar el cache de la tabla reversa
    function updateReversaCache(data) {
      if (!tableCache['reversa']) {
        tableCache['reversa'] = [];
      }
      
      // Verificar si el registro ya existe en el cache
      const existingIndex = tableCache['reversa'].findIndex(item => {
        const itemSerial = item.Serial || item.serie || '';
        return itemSerial === data.serial;
      });
      
      // Para id_movimiento=12 (entrega declarada), semantica='Entregado por Validar', o id_movimiento=11 (rechazo de bodega), eliminar del cache
      if (data.id_movimiento == 12 || data.semantica === 'Entregado por Validar' || (data.id_movimiento == 11 && data.semantica === 'Rechaza Bodega')) {
        if (existingIndex !== -1) {
          // Eliminar el registro del cache
          tableCache['reversa'].splice(existingIndex, 1);
          console.log('🗑️ Registro eliminado del cache de reversa:', data.serial);
        }
        return;
      }
      
      // Crear objeto de datos para el cache con información adicional
      const newCacheItem = {
        Serial: data.serial,
        serie: data.serial,
        Semantica: data.semantica || 'Reversa',
        id_movimiento: data.id_movimiento,
        id_tecnico_destino: data.id_tecnico_destino || '',
        id_tecnico_origen: data.id_tecnico_origen || '',
        ticket: data.ticket || '',
        fecha_hora: data.fecha_hora || new Date().toISOString().replace('T', ' ').substring(0, 19)
      };
      
      // Para transferencias a supervisor (id_movimiento = 6, 7), actualizar con estado especial
      if (data.id_movimiento == 6 || data.id_movimiento == 7) {
        newCacheItem.Semantica = 'Pendiente de revisión';
      }
      
      if (existingIndex !== -1) {
        // Actualizar registro existente
        tableCache['reversa'][existingIndex] = newCacheItem;
        console.log('🔄 Registro actualizado en cache de reversa:', data.serial);
      } else {
        // Agregar nuevo registro
        tableCache['reversa'].push(newCacheItem);
        console.log('➕ Nuevo registro agregado al cache de reversa:', data.serial);
      }
    }

    // Función para mantener la sesión activa (previene timeouts)
    function keepSessionAlive() {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', 'session_ping.php?t=' + new Date().getTime(), true);
      xhr.send();
    }

    // Función para limpiar y cerrar SSE al salir de la página
    function cleanupSSE() {
      if (eventSource) {
        eventSource.close();
        eventSource = null;
      }
    }

    // Agregar data-serial a las filas para facilitar la identificación SSE
    function addSerialDataToRows() {
      document.querySelectorAll('.data-table tbody tr').forEach(row => {
        const serieCell = row.querySelector('td:first-child');
        if (serieCell && !row.getAttribute('data-serial')) {
          const serieText = serieCell.textContent.trim();
          if (serieText && serieText !== 'N/A') {
            row.setAttribute('data-serial', serieText);
          }
        }
      });
    }

    // Modificar setupActionButtons para incluir data-serial y configurar botón instalación
    const originalSetupActionButtons = setupActionButtons;
    setupActionButtons = function() {
      originalSetupActionButtons();
      addSerialDataToRows();
      setupInstalaButton(); // Configurar botón de instalación cada vez que se configuran los botones
      setupReversaButtons(); // Configurar botones específicos para la tabla de reversa
    };
    
    // Función para configurar los botones específicos de la tabla de reversa
    function setupReversaButtons() {
      // Configurar botón de declarar entrega reversa
      document.querySelectorAll('.declarar-rev-btn').forEach(button => {
        button.removeEventListener('click', handleDeclararReversaClick);
        button.addEventListener('click', handleDeclararReversaClick);
      });
      
      // Configurar botón de transferir a supervisor
      document.querySelectorAll('.transferir-rev-btn').forEach(button => {
        button.removeEventListener('click', handleTransferirReversaClick);
        button.addEventListener('click', handleTransferirReversaClick);
      });
      
      console.log('🔄 Botones de reversa configurados');
    }
    
    // Handler para botón de declarar entrega reversa
    function handleDeclararReversaClick(e) {
      e.preventDefault();
      const row = this.closest('tr');
      const serial = row.getAttribute('data-serial') || row.cells[0].textContent;
      const idMovimiento = this.getAttribute('data-id-movimiento') || '0';
      console.log('📦 Clic en declarar entrega reversa:', { serial, idMovimiento });
      console.log('🔍 [REVERSA_LOG] Iniciando proceso de entrega reversa para serie:', serial, 'con ID movimiento:', idMovimiento);
      redirigirEnTransferencia(serial, serial, idMovimiento, 'ENTREGA_REV');
    }
    
    // Handler para botón de transferir a supervisor
    function handleTransferirReversaClick(e) {
      e.preventDefault();
      const row = this.closest('tr');
      const serial = row.getAttribute('data-serial') || row.cells[0].textContent;
      const idMovimiento = this.getAttribute('data-id-movimiento') || '0';
      console.log('📤 Clic en transferir a supervisor:', { serial, idMovimiento });
      redirigirEnTransferencia(serial, serial, idMovimiento, 'TRANSFIERE_REV');
    }
    
    // Handler para botón de declarar entregada en el offcanvas
    function handleReversaDeclaraClick(e) {
      e.preventDefault();
      console.log('📦 Procesando declaración de entrega reversa');
      console.log('🔍 [REVERSA_LOG] Iniciando proceso de declaración de entrega reversa');
      
      // Obtener valores de los campos
      const serial = document.getElementById('serieReversaDeclara').value;
      const fileInput = document.getElementById('fileReversaDecla');
      
      console.log('🔍 [REVERSA_LOG] Valores obtenidos - Serie:', serial, ', Archivo:', fileInput ? (fileInput.files.length > 0 ? 'Disponible' : 'No seleccionado') : 'Input no encontrado');
      
      // Validar campos
      if (!serial) {
        console.log('⚠️ [REVERSA_LOG] Error: Falta la serie del material');
        mostrarNotificacion('Falta la serie del material', 'warning');
        return;
      }
      
      if (!fileInput || !fileInput.files[0]) {
        console.log('⚠️ [REVERSA_LOG] Error: No se adjuntó ningún archivo');
        mostrarNotificacion('Debe adjuntar un archivo para la entrega', 'warning');
        return;
      }
      
      console.log('✅ [REVERSA_LOG] Validación completada correctamente');
      
      // Mostrar indicador de carga
      this.disabled = true;
      this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Procesando...';
      
      // Preparar datos para envío
      const formData = new FormData();
      formData.append('Serial', serial);
      formData.append('accion', 'ENTREGA_REV');
      const idTecnico = <?php echo $id_usuario; ?>;
      formData.append('id_tecnico_origen', idTecnico);
      formData.append('fileReversaDecla', fileInput.files[0]);
      
      console.log('🔍 [REVERSA_LOG] FormData preparado:', {
        serial: serial,
        accion: 'ENTREGA_REV',
        id_tecnico_origen: idTecnico,
        archivo: fileInput.files[0].name
      });
      
      // Envío AJAX
      const xhr = new XMLHttpRequest();
      xhr.open('POST', 'GET_LOGIS_DIRECTA.php');
      
      xhr.onload = () => {
        this.disabled = false;
        this.innerHTML = 'Declarar entregada';
        console.log('🔄 [REVERSA_LOG] Respuesta recibida - Status:', xhr.status);
        
        if (xhr.status === 200) {
          console.log('✅ Entrega reversa procesada:', xhr.responseText);
          console.log('🔍 [REVERSA_LOG] Respuesta completa:', xhr.responseText);
          
          // Cerrar offcanvas
          const offcanvasElement = document.getElementById('offcanvasReversaDeclara');
          if (offcanvasElement) {
            const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
            if (offcanvas) {
              offcanvas.hide();
            }
          }
          
          // Mostrar notificación de éxito
          mostrarNotificacion('Material entregado correctamente', 'success');
          
          // Eliminar solo la fila específica de la tabla de reversa
          console.log('🗑️ Eliminando fila específica en reversa para:', serial);
          
          // Eliminar del DOM con animación suave
          const reversaTableBody = document.getElementById('reversaTableBody');
          if (reversaTableBody) {
            // Buscar por atributo data-serial
            const rowsToRemove = reversaTableBody.querySelectorAll(`tr[data-serial="${serial}"]`);
            if (rowsToRemove.length > 0) {
              console.log(`🗑️ Encontradas ${rowsToRemove.length} filas para eliminar`);
              rowsToRemove.forEach(row => {
                // Usar la función que ya tenemos para animación suave
                removeRowWithAnimation(row);
              });
            } else {
              // Búsqueda alternativa como respaldo
              console.log('🔍 Buscando por contenido de celda...');
              const rows = reversaTableBody.getElementsByTagName('tr');
              for (let i = 0; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                if (cells.length > 0 && cells[0].innerText === serial) {
                  removeRowWithAnimation(rows[i]);
                  break; // Solo eliminamos la primera coincidencia
                }
              }
            }
          }
          
          // Actualizar el caché de forma limpia
          if (tableCache['reversa'] && Array.isArray(tableCache['reversa'])) {
            const initialLength = tableCache['reversa'].length;
            tableCache['reversa'] = tableCache['reversa'].filter(item => {
              const itemSerial = item.Serial || item.serie || '';
              return itemSerial !== serial;
            });
            const removedCount = initialLength - tableCache['reversa'].length;
            console.log(`🗑️ ${removedCount} registros eliminados del cache de reversa`);
          }
        } else {
          console.error('❌ Error al procesar entrega reversa:', xhr.status, xhr.statusText);
          console.error('🚨 [REVERSA_LOG] Error en respuesta del servidor:', xhr.status, xhr.statusText, xhr.responseText);
          mostrarNotificacion('Error al procesar la entrega. Inténtelo de nuevo.', 'danger');
        }
      };
      
      xhr.onerror = () => {
        this.disabled = false;
        this.innerHTML = 'Declarar entregada';
        console.error('❌ Error de red al procesar entrega reversa');
        console.error('🚨 [REVERSA_LOG] Error de red en la solicitud AJAX');
        mostrarNotificacion('Error de conexión. Verifique su red e intente nuevamente.', 'danger');
      };
      
      xhr.onreadystatechange = () => {
        console.log('🔍 [REVERSA_LOG] Estado de la solicitud AJAX:', xhr.readyState);
      };
      
      console.log('📤 [REVERSA_LOG] Enviando solicitud AJAX...');
      xhr.send(formData);
      console.log('✉️ [REVERSA_LOG] Solicitud AJAX enviada');
    }
    
    // Handler para botón de solicitar requerimiento en transferencia reversa
    function handleTransferReversaClick(e) {
      e.preventDefault();
      console.log('📤 Procesando transferencia reversa');
      
      // Obtener valores de los campos
      const serial = document.getElementById('serie_trans_rever').value;
      const rut = document.getElementById('rutReversa').value;
      const orden = document.getElementById('ordenReversa').value;
      const supervisorId = document.getElementById('super_destino').value;
      const motivo = document.getElementById('listReversa').value;
      const serieFisica = document.getElementById('serieNewReversa').value;
      const observaciones = document.getElementById('obs_rev_tra').value;
      const fileInput = document.getElementById('userfile');
      
      // Validaciones
      if (!serial) {
        mostrarNotificacion('Falta la serie del material', 'warning');
        return;
      }
      
      if (!orden) {
        mostrarNotificacion('Debe ingresar la orden de trabajo', 'warning');
        return;
      }
      
      if (!supervisorId) {
        mostrarNotificacion('Debe asignar un supervisor de destino', 'warning');
        return;
      }
      
      if (!motivo || motivo === '') {
        mostrarNotificacion('Debe seleccionar un motivo', 'warning');
        return;
      }
      
      if (!serieFisica) {
        mostrarNotificacion('Debe ingresar la serie física retirada', 'warning');
        return;
      }
      
      if (!observaciones || observaciones.length < 10) {
        mostrarNotificacion('Las observaciones deben tener al menos 10 caracteres', 'warning');
        return;
      }
      
      // Mostrar indicador de carga
      this.disabled = true;
      this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Procesando...';
      
      // Preparar datos para envío
      const formData = new FormData();
      formData.append('Serial', serial);
      formData.append('accion', 'TRANSFIERE_REV');
      formData.append('id_tecnico_origen', <?php echo $id_usuario; ?>);
      formData.append('id_tecnico_destino', supervisorId);
      formData.append('rutReversa', rut);
      formData.append('ordenReversa', orden);
      formData.append('listReversa', motivo);
      formData.append('serieNewReversa', serieFisica);
      formData.append('obs_rev_tra', observaciones);
      
      if (fileInput && fileInput.files[0]) {
        formData.append('userfile', fileInput.files[0]);
      }
      
      // Envío AJAX
      const xhr = new XMLHttpRequest();
      xhr.open('POST', 'GET_LOGIS_DIRECTA.php');
      
      xhr.onload = () => {
        this.disabled = false;
        this.innerHTML = 'Solicitar requerimiento';
        
        if (xhr.status === 200) {
          console.log('✅ Transferencia reversa procesada:', xhr.responseText);
          
          // Cerrar offcanvas
          const offcanvasElement = document.getElementById('offcanvasrevSuper');
          if (offcanvasElement) {
            const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
            if (offcanvas) {
              offcanvas.hide();
            }
          }
          
          // Mostrar notificación de éxito
          mostrarNotificacion('Transferencia reversa procesada correctamente', 'success');
          
          // Recargar tabla de reversa
          if (typeof loadTableData === 'function') {
            loadTableData('reversa');
          }
        } else {
          console.error('❌ Error al procesar transferencia reversa:', xhr.status, xhr.statusText);
          mostrarNotificacion('Error al procesar la transferencia. Inténtelo de nuevo.', 'danger');
        }
      };
      
      xhr.onerror = () => {
        this.disabled = false;
        this.innerHTML = 'Solicitar requerimiento';
        console.error('❌ Error de red al procesar transferencia reversa');
        mostrarNotificacion('Error de conexión. Verifique su red e intente nuevamente.', 'danger');
      };
      
      xhr.send(formData);
    }

    // Función transferirRegistroInstala para instalación (con nombre distinto para evitar conflictos)
    function transferirRegistroInstala(Serial, ticket, id_tecnico, tecnico_destino, motivo, accion) {
      console.log('📤 transferirRegistroInstala:', { Serial, ticket, id_tecnico, tecnico_destino, motivo, accion });
      
      var formData = new FormData();
      const hiddenInputValue = document.getElementById('usuario_destino_hidden') ? 
                               document.getElementById('usuario_destino_hidden').value : '';
      
      // Condición para manejar el archivo si la acción es 'instala'
      if (accion === 'instala') {
        const fileInput = document.getElementById('fileInsta');
        if (fileInput && fileInput.files[0]) {
          formData.append('fileInsta', fileInput.files[0]);
        }
        const rut_instalado = document.getElementById('rut_insta').value;
        const OT_instala = document.getElementById('formOT_insta').value;
        formData.append('rut_instalado', rut_instalado);
        formData.append('OT_instala', OT_instala);
      }
      
      formData.append('Serial', Serial);
      formData.append('accion', accion);
      formData.append('id_tecnico_origen', id_tecnico);
      formData.append('id_tecnico_destino', tecnico_destino);
      if (hiddenInputValue) {
        formData.append('id_tecnico_destino', hiddenInputValue);
      } else {
        formData.append('id_tecnico_destino', tecnico_destino);
      }
      formData.append('ticket', ticket);
      formData.append('motivo', motivo);
      
      // Crear tabla para debug
      console.log('%c📝 DATOS A ENVIAR (INSTALACIÓN):', 'background: #2196F3; color: white; font-size: 12px; padding: 5px; border-radius: 5px;');
      const formDataObj = {};
      formData.forEach(function(value, key) {
        formDataObj[key] = value;
        console.log(`- ${key}: ${value}`);
      });
      console.table(formDataObj);
      
      // Realiza la solicitud POST utilizando AJAX a GET_LOGIS_DIRECTA.php
      var request = new XMLHttpRequest();
      request.open('POST', 'GET_LOGIS_DIRECTA.php');
      request.onload = function() {
        // Procesa la respuesta del servidor si es necesario
        if (request.status === 200) {
          // Maneja la respuesta del servidor
          console.log('✅ Respuesta del servidor:', request.responseText);
          alert("Datos enviados exitosamente.");
          
          // Cerrar offcanvas
          const offcanvasElement = document.getElementById('offcanvasInstala');
          if (offcanvasElement) {
            const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
            if (offcanvas) {
              offcanvas.hide();
            }
          }
          
          // Limpiar formulario
          document.getElementById('formOT_insta').value = '';
          document.getElementById('rut_insta').value = '';
          document.getElementById('obs_insta').value = '';
          document.getElementById('fileInsta').value = '';
          
        } else {
          console.error('❌ Error en la respuesta del servidor:', request.status, request.statusText);
          alert("Error al enviar los datos. Código: " + request.status);
        }
      };
      request.onerror = function() {
        console.error('❌ Error de red al enviar los datos');
        alert("Error de red al enviar los datos.");
      };
      request.send(formData);
    }

    // Event listener para el botón "Declarar instalada"
    function setupInstalaButton() {
      const instalButton = document.getElementById('instalButton');
      if (instalButton) {
        // Remover listener previo
        instalButton.removeEventListener('click', handleInstalaButtonClick);
        // Agregar nuevo listener
        instalButton.addEventListener('click', handleInstalaButtonClick);
      }
    }

    function handleInstalaButtonClick() {
      const serie = document.getElementById('serie_insta').value;
      const rut = document.getElementById('rut_insta').value;
      const ot = document.getElementById('formOT_insta').value;
      const motivo = document.getElementById('obs_insta').value;
      const ticket = ''; // Agrega el valor del ticket aquí
      const tecnico_origen = <?php echo $id_usuario; ?>;
      const tecnico_destino = <?php echo $id_usuario; ?>;
      
      // Verificar si ambos campos tienen valores ingresados
      if (ot.trim() !== '' && motivo.trim() !== '') {
        // Validar longitud mínima de observaciones (como en el original)
        if (motivo.length < 10) {
          alert('Las observaciones deben tener al menos 10 caracteres');
          return;
        }
        
        // Llamada a la función transferirRegistroInstala con los parámetros
        transferirRegistroInstala(serie, ticket, tecnico_origen, tecnico_destino, motivo, 'instala');
      } else {
        // Mostrar un mensaje de error si los campos no tienen valores ingresados
        alert('Por favor, complete todos los campos requeridos (Orden de Trabajo y Observaciones)');
      }
    }

    // Inicializar SSE después de que el DOM esté listo
    document.addEventListener('DOMContentLoaded', function() {
      // Configurar botón de instalación
      setupInstalaButton();
      
      // Inicializar SSE después de un breve delay para asegurar que todo esté cargado
      setTimeout(() => {
        initSSE();
        
        // Mantener sesión activa cada 5 minutos
        setInterval(keepSessionAlive, 300000);
      }, 1000);
    });

    // Limpiar SSE al salir de la página
    window.addEventListener('beforeunload', cleanupSSE);
    window.addEventListener('unload', cleanupSSE);

    // Funciones para manejo de rechazo de material (replicadas desde original)
    function rechazoMaterial(Serial, ticket, id_tecnico_destino, accion) {
      console.log('🚫 Iniciando rechazo de material:', { Serial, ticket, id_tecnico_destino, accion });
      
      // Poblar los campos ocultos del modal
      document.getElementById('popupSerial').value = Serial;
      document.getElementById('popupTicket').value = ticket;
      document.getElementById('popupIdTecnicoDestino').value = id_tecnico_destino;
      document.getElementById('popupAccion').value = accion;
      
      // Mostrar el modal
      const popupContainer = document.getElementById('popup-container');
      if (popupContainer) {
        popupContainer.style.display = 'flex';
      }
    }

    function Rechazoaceptar() {
      console.log('✅ Procesando rechazo aceptado');
      
      // Obtener valores del modal
      const serial = document.getElementById('popupSerial').value;
      const ticket = document.getElementById('popupTicket').value;
      const idTecnicoDestino = document.getElementById('popupIdTecnicoDestino').value;
      const accion = document.getElementById('popupAccion').value;
      const motivoRechazo = document.getElementById('motivoRechazo').value;
      
      // Validar que se haya seleccionado un motivo
      if (!motivoRechazo || motivoRechazo === '') {
        alert('Por favor seleccione un motivo de rechazo');
        return;
      }
      
      console.log('📤 Enviando datos de rechazo:', { serial, ticket, idTecnicoDestino, accion, motivoRechazo });
      
      // Crear XMLHttpRequest para enviar datos
      const xhr = new XMLHttpRequest();
      xhr.open('POST', 'GET_LOGIS_DIRECTA.php', true);
      xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
      
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            console.log('📥 Respuesta cruda del servidor:', xhr.responseText);
            
            // Verificar si la respuesta contiene JSON
            if (xhr.responseText.trim().startsWith('{') || xhr.responseText.trim().startsWith('[')) {
              try {
                const response = JSON.parse(xhr.responseText);
                console.log('✅ Respuesta JSON parseada:', response);
                
                if (response.success) {
                  console.log('🎉 Rechazo procesado exitosamente');
                  
                  // Cerrar modal
                  Rechazocancelar();
                  
                  // Actualizar cache para eliminar el registro rechazado
                  updateCacheAfterRejection(serial);
                  
                  mostrarNotificacion('Material rechazado correctamente', 'success');
                } else {
                  console.error('❌ Error en el servidor:', response.message);
                  alert('Error al procesar el rechazo: ' + (response.message || 'Error desconocido'));
                }
              } catch (e) {
                console.error('❌ Error al parsear JSON:', e);
                console.log('Contenido que no se pudo parsear:', xhr.responseText);
                
                // Si no es JSON válido pero la respuesta llegó, asumir éxito
                console.log('⚠️ Respuesta no es JSON, asumiendo éxito');
                Rechazocancelar();
                updateCacheAfterRejection(serial);
                mostrarNotificacion('Material rechazado correctamente', 'success');
              }
            } else {
              // Respuesta no es JSON, probablemente texto plano - asumir éxito si llegó
              console.log('⚠️ Respuesta no es JSON, contenido:', xhr.responseText);
              Rechazocancelar();
              updateCacheAfterRejection(serial);
              mostrarNotificacion('Material rechazado correctamente', 'success');
            }
          } else {
            console.error('❌ Error HTTP:', xhr.status);
            alert('Error de conexión con el servidor');
          }
        }
      };
      
      // Preparar datos para envío (usando los nombres que espera GET_LOGIS_DIRECTA.php)
      const currentUserId = userId || '<?php echo $id_usuario; ?>';
      const formData = 'Serial=' + encodeURIComponent(serial) +
                      '&ticket=' + encodeURIComponent(ticket || serial) +
                      '&id_tecnico_origen=' + encodeURIComponent(ticket || currentUserId) +
                      '&id_tecnico_destino=' + encodeURIComponent(idTecnicoDestino) +
                      '&accion=' + encodeURIComponent(accion) +
                      '&motivo=' + encodeURIComponent(motivoRechazo);
      
      console.log('📋 Datos finales enviados:', {
        Serial: serial,
        ticket: ticket || serial,
        id_tecnico_origen: ticket || currentUserId,
        id_tecnico_destino: idTecnicoDestino,
        accion: accion,
        motivo: motivoRechazo
      });
      
      xhr.send(formData);
    }

    function Rechazocancelar() {
      console.log('❌ Cancelando rechazo');
      
      // Ocultar el modal
      const popupContainer = document.getElementById('popup-container');
      if (popupContainer) {
        popupContainer.style.display = 'none';
      }
      
      // Limpiar campos del modal
      document.getElementById('popupSerial').value = '';
      document.getElementById('popupTicket').value = '';
      document.getElementById('popupIdTecnicoDestino').value = '';
      document.getElementById('popupAccion').value = '';
      document.getElementById('motivoRechazo').selectedIndex = 0;
    }

    // Función para actualizar cache después del rechazo
    function updateCacheAfterRejection(serialNumber) {
      console.log('🗑️ Actualizando cache después del rechazo para:', serialNumber);
      
      if (tableCache['recepcion'] && Array.isArray(tableCache['recepcion'])) {
        tableCache['recepcion'] = tableCache['recepcion'].filter(item => {
          const itemSerial = item.Serial || item.serie || '';
          return itemSerial !== serialNumber;
        });
        console.log('✅ Cache de recepción actualizado, registro eliminado');
      }
    }

    // Cerrar modal al hacer clic fuera de él
    document.addEventListener('click', function(e) {
      const popupContainer = document.getElementById('popup-container');
      const popup = document.getElementById('popup');
      
      if (popupContainer && e.target === popupContainer && !popup.contains(e.target)) {
        Rechazocancelar();
      }
    });
  </script>

  <?php
  // Incluir el script para mantener la sesión activa (igual que activity_dashboard.php)
  incluirScriptSesion();
  ?>
  
  <!-- El script personalizado ya se carga en la sección de scripts -->
</body>
</html>